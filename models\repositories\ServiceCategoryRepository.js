const sequelize = require('sequelize');
const { ServiceCategory, ServiceType } = require('..');
const {
  successMessage,
  errorMessage,
  serviceStatus,
} = require('../../config/options');
const { Op } = sequelize;

const checkDuplicateServiceCategory = async (body, existingCategory = null) => {
  try {
    if (!body.name) return null;

    return await ServiceCategory.findOne({
      where: {
        name: body.name,
        ...(existingCategory?.id && {
          id: { [Op.ne]: existingCategory.id },
        }),
      },
      attributes: ['id', 'name', 'status'],
    });
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndCreateServiceCategory = async (body) => {
  try {
    const existingCategory = await checkDuplicateServiceCategory(body);

    if (!existingCategory) {
      const serviceCategory = await ServiceCategory.create(body);
      return {
        success: true,
        message: successMessage.SAVED_SUCCESS_MESSAGE('Service category'),
        data: serviceCategory,
      };
    }

    return {
      success: false,
      message: errorMessage.ALREADY_EXIST('Service category name'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndUpdateServiceCategory = async (serviceCategoryId, body) => {
  try {
    const existingCategory = await ServiceCategory.findOne({
      where: { id: serviceCategoryId },
    });

    if (!existingCategory) {
      return {
        success: false,
        message: errorMessage.NO_USER('Service category'),
      };
    }

    const duplicateCategory = await checkDuplicateServiceCategory(
      body,
      existingCategory
    );
    if (duplicateCategory) {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST('Service category name'),
      };
    }

    Object.assign(existingCategory, body);

    await existingCategory.save();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Service category'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

const getServiceCategoryAndCount = async ({
  start = 0,
  limit = 10,
  search = null,
  serviceId,
}) => {
  try {
    const where = {
      ...(search && { name: { [Op.iLike]: `%${search}%` } }),
      ...(serviceId && { serviceId }),
    };

    const { count, rows } = await ServiceCategory.findAndCountAll({
      where,
      offset: Number(start),
      limit: Number(limit),
      order: [
        ['id', 'ASC'],
        [{ model: ServiceType, as: 'serviceTypes' }, 'status', 'ASC'],
      ],
      attributes: ['id', 'name', 'status', 'createdAt', 'updatedAt'],
      include: [
        {
          model: ServiceType,
          as: 'serviceTypes',
          required: false,
          attributes: ['id', 'categoryId', 'name', 'imageUrl', 'status'],
        },
      ],
    });

    return {
      message: successMessage.DETAIL_MESSAGE('Service categories'),
      data: {
        rows,
        pagination: {
          totalCount: count,
          start: Number(start),
          limit: Number(limit),
        },
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndUpdateStatus = async (serviceCategoryId, isDeleted = false) => {
  try {
    const existingServiceCategory = await ServiceCategory.findOne({
      where: {
        id: serviceCategoryId,
      },
      attributes: [
        'id',
        'name',
        'imageUrl',
        'status',
        'createdAt',
        'updatedAt',
      ],
    });

    if (!existingServiceCategory) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Service category'),
      };
    }
    if (isDeleted) {
      existingServiceCategory.status = serviceStatus.DELETED;
    } else {
      existingServiceCategory.status =
        serviceStatus.ACTIVE === existingServiceCategory.status
          ? serviceStatus.BLOCKED
          : serviceStatus.ACTIVE;
    }
    await existingServiceCategory.save();

    return {
      success: true,
      message: successMessage.CHANGED_SUCCESS_MESSAGE(
        'Service category status'
      ),
      ...(!isDeleted && {
        data: existingServiceCategory,
      }),
    };
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndPatchServiceCategoryStatus = async (id, isDelete) => {
  try {
    const { success, message } = await checkAndUpdateStatus(id, isDelete);
    return {
      success,
      message,
    };
  } catch (error) {
    throw new Error(error);
  }
};

module.exports = {
  checkAndCreateServiceCategory,
  checkAndUpdateServiceCategory,
  getServiceCategoryAndCount,
  checkAndPatchServiceCategoryStatus,
};
