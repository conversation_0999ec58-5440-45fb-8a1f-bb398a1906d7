const { generateCloudFrontUrl } = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const BusinessDocument = sequelize.define(
    'BusinessDocument',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      filePath: {
        allowNull: true,
        type: DataTypes.TEXT,
        get() {
          return generateCloudFrontUrl(this.getDataValue('filePath'));
        },
        set(file) {
          if (file) {
            this.setDataValue(
              'filePath',
              `uploads/${file.split('uploads/')[1]}`
            );
          }
        },
      },
      fileName: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      fileSize: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      fileType: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      fileCategory: {
        type: DataTypes.STRING,
        allowNull: true,
      },
    },
    {
      tableName: 'BusinessDocument',
      timestamps: true,
    }
  );

  BusinessDocument.associate = function (models) {
    BusinessDocument.belongsTo(models.BusinessInformation, {
      foreignKey: 'businessId',
      as: 'business',
      onDelete: 'CASCADE',
    });
  };

  return BusinessDocument;
};
