require('dotenv/config');

const { join } = require('path');
const express = require('express');
const { static: serveStatic } = express;
const compression = require('compression');
const logger = require('morgan');
const chalk = require('chalk');
const errorHandler = require('errorhandler');

const lusca = require('lusca');
const { xframe, xssProtection } = lusca;

const cors = require('cors');
const apiDoc = require('./api-docs/index.js');
const indexRouter = require('./routes/index.js');
const initializeConnectionHelper = require('./models/helpers/InitializeConnectionHelper.js');

/**
 * Create Express server.
 */
const app = express();

// GZIP compress resources served
app.use(compression());

app.use(cors());

/**
 * Start Express server.
 */
app.set('port', process.env.PORT || 3000);
const server = app.listen(app.get('port'), () => {
  console.log(
    '%s App is running at http://localhost:%d in %s mode',
    chalk.green('✓'),
    app.get('port'),
    app.get('env')
  );
  console.log('Press CTRL-C to stop\n');
});

/**
 * Express configuration.
 */
if (process.env.NODE_ENV !== 'production') {
  app.use(logger('dev'));
}

if (process.env.NODE_ENV !== 'production') {
  app.use('/api-docs', apiDoc);
}

app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ limit: '50mb', extended: true }));
app.use(xframe('SAMEORIGIN'));
app.use(xssProtection(true));
app.disable('x-powered-by');

app.use(serveStatic(join('./public'), { maxAge: 31557600000 }));

app.set('views', join('./public'));
app.set('view engine', 'pug');

// application specific logging, throwing an error, or other logic here
process.on('unhandledRejection', (reason, p) => {
  console.log('Unhandled Rejection at: Promise', p, 'reason:', reason);
});

app.use((req, res, next) => {
  if (process.env.NODE_ENV === 'development') {
    console.log('req body', req.body);
    console.log('req query', req.query);
    // console.log('authorization', req.headers);
  }
  next();
});
// Routes
app.use('/', indexRouter);

initializeConnectionHelper(server, app);

/**
 * Error Handler.
 */
if (process.env.NODE_ENV !== 'production') {
  app.use(errorHandler());
}

module.exports = app;
