module.exports = (sequelize, DataTypes) => {
  const ServiceRequestOption = sequelize.define(
    'ServiceRequestOption',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
    },
    {
      tableName: 'ServiceRequestOption',
      timestamps: true,
    }
  );

  ServiceRequestOption.associate = function (models) {
    ServiceRequestOption.belongsTo(models.ServiceRequest, {
      foreignKey: 'requestId',
      as: 'serviceRequest',
      onDelete: 'CASCADE',
    });

    ServiceRequestOption.belongsTo(models.ServiceOption, {
      foreignKey: 'serviceOptionId',
      as: 'serviceOption',
      onDelete: 'CASCADE',
    });
  };

  return ServiceRequestOption;
};
