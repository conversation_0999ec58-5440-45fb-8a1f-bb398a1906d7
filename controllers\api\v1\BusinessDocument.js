const BusinessDocumentRepository = require('../../../models/repositories/BusinessDocumentRepository');
const { genRes, errorMessage, resCode } = require('../../../config/options');

exports.postBusinessDocument = async (req, res) => {
  try {
    const businessId = req.user.businessId;
    const { documents } = req.body;

    const { success, message, data } =
      await BusinessDocumentRepository.checkAndCreateBusinessDocument(
        businessId,
        documents
      );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.putBusinessDocument = async (req, res) => {
  try {
    const businessId = req.user.businessId;
    const { documents } = req.body;

    const { success, message, data } =
      await BusinessDocumentRepository.checkAndUpdateBusinessDocument(
        businessId,
        documents
      );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.getBusinessDocument = async (req, res) => {
  try {
    const businessId = req.user.businessId;

    const { success, message, data } =
      await BusinessDocumentRepository.checkAndGetBusinessDocuments(businessId);
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }
    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};
