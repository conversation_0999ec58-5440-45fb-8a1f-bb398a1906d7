paths:
  /shared/upload:
    post :
      tags:
        - "Shared"
      summary: "Upload media"
      description: "Uploads a file to the server using multipart/form-data."
      operationId: "uploadMedia"
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: "The file to be uploaded."
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "File uploaded successfully"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"       
  /shared/signed-url:
      get :
        tags:
          - "Shared"
        summary: "Signed url"
        description: ""
        operationId: "generateUrl"
        produces:
          - "application/json"
        parameters: 
          - in: query
            name: fileName
            schema:
                type: string
            description: file name
            required: true
        security:
          - bearerAuth: []
        responses:
          "200":
            description: "signed url"
          "400":
            description: "Invalid Request"
          "500":
            description: "Internal Server Error"
  
  /shared/deals-on-wheels:
    get:
      tags:
        - "Shared"
      summary: "Get all Deals On Wheels"
      description: "List all Deals On Wheels entries with pagination."
      operationId: "getSharedDealsOnWheels"
      parameters:
        - name: start
          in: query
          required: false
          schema:
            type: integer
            default: 0
          description: "Pagination offset (default: 0)"
        - name: limit
          in: query
          required: false
          schema:
            type: integer
            default: 10
          description: "Number of entries to return (default: 10)"
      responses:
        "200":
          description: "List of Deals On Wheels entries fetched successfully."
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"

  /shared/tts:
    post:
      tags:
        - "Shared"
      summary: "Text to Speech (TTS)"
      description: "Converts text to speech using ElevenLabs API and returns an audio stream."
      operationId: "textToSpeech"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                text:
                  type: string
                  description: "The text to convert to speech."
                voiceId:
                  type: string
                  description: "(Optional) The ElevenLabs voice ID to use. Defaults to the configured default."
              required:
                - text
      responses:
        "200":
          description: "Audio stream (MPEG)"
          content:
            audio/mpeg:
              schema:
                type: string
                format: binary
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"