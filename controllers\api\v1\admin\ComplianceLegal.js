const ComplianceLegalRepository = require('../../../../models/repositories/ComplianceLegalRepository');
const { genRes, errorMessage, resCode } = require('../../../../config/options');

exports.postComplianceLegal = async (req, res) => {
  try {
    const userId = req.user.id;

    const { success, message, data } =
      await ComplianceLegalRepository.checkAndCreateComplianceLegal(
        userId,
        req.body
      );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.getComplianceLegalListing = async (req, res) => {
  try {
    const { start = 0, limit = 10, search = null } = req.query;

    const { message, data } =
      await ComplianceLegalRepository.getComplianceLegalCount({
        start: parseInt(start),
        limit: parseInt(limit),
        search,
      });

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.getComplianceLegalById = async (req, res) => {
  try {
    const complianceId = req.params.id;

    const { success, message, data } =
      await ComplianceLegalRepository.checkAndGetComplianceLegal(complianceId);

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.putComplianceLegal = async (req, res) => {
  try {
    const complianceId = req.params.id;

    const { success, message, data } =
      await ComplianceLegalRepository.checkAndUpdateComplianceLegal(
        complianceId,
        req.body
      );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};
