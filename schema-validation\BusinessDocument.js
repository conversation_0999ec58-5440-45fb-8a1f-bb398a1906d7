const { fileType, fileCategory } = require('../config/options');

exports.addBusinessDocuments = {
  documents: {
    in: ['body'],
    isArray: {
      errorMessage: 'Documents must be an array',
    },
    notEmpty: {
      errorMessage: 'Documents array cannot be empty',
    },
  },
  'documents.*.fileName': {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'fileName is required',
    },
    isString: {
      errorMessage: 'fileName must be a string',
    },
  },
  'documents.*.fileSize': {
    in: ['body'],
    notEmpty: {
      errorMessage: 'fileSize is required',
    },
    isInt: {
      errorMessage: 'fileSize must be an integer',
    },
  },
  'documents.*.fileType': {
    in: ['body'],
    trim: true,
    isIn: {
      options: [Object.values(fileType)],
      errorMessage: `File type must be one of: ${Object.values(fileType).join(', ')}`,
    },
    notEmpty: {
      errorMessage: 'fileType is required',
    },
    isString: {
      errorMessage: 'fileType must be a string',
    },
  },
  'documents.*.fileCategory': {
    in: ['body'],
    optional: true,
    isIn: {
      options: [Object.values(fileCategory)],
      errorMessage: `File category must be one of: ${Object.values(fileCategory).join(', ')}`,
    },
    trim: true,
    isString: {
      errorMessage: 'fileCategory must be a string',
    },
  },
  'documents.*.filePath': {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'filePath is required',
    },
    isString: {
      errorMessage: 'filePath must be a string',
    },
  },
};

exports.updateBusinessDocuments = {
  documents: {
    in: ['body'],
    isArray: {
      errorMessage: 'Documents must be an array',
    },
    notEmpty: {
      errorMessage: 'Documents array cannot be empty',
    },
  },
  'documents.*.id': {
    in: ['body'],
    notEmpty: {
      errorMessage: 'id is required for updating document',
    },
    isInt: {
      errorMessage: 'id must be an integer',
    },
  },
  'documents.*.fileName': {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'fileName must be a string',
    },
  },
  'documents.*.fileSize': {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'fileSize must be an integer',
    },
  },
  'documents.*.fileType': {
    in: ['body'],
    optional: true,
    trim: true,
    isIn: {
      options: [Object.values(fileType)],
      errorMessage: `fileType must be one of: ${Object.values(fileType).join(', ')}`,
    },
    isString: {
      errorMessage: 'fileType must be a string',
    },
  },
  'documents.*.fileCategory': {
    in: ['body'],
    optional: true,
    trim: true,
    isIn: {
      options: [Object.values(fileCategory)],
      errorMessage: `fileCategory must be one of: ${Object.values(fileCategory).join(', ')}`,
    },
    isString: {
      errorMessage: 'fileCategory must be a string',
    },
  },
  'documents.*.filePath': {
    in: ['body'],
    optional: true,
    trim: true,
    notEmpty: {
      errorMessage: 'filePath cannot be empty',
    },
    isString: {
      errorMessage: 'filePath must be a string',
    },
  },
};
