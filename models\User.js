'use strict';
const bcrypt = require('bcryptjs'); // Uncomment if implementing password hashing
const jwt = require('jsonwebtoken'); // Uncomment if implementing JWT
const { userStatus, usersRoles } = require('../config/options');
const { Op } = require('sequelize');
const jwtOptions = require('../config/jwtOptions');
const OPTIONS = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const User = sequelize.define(
    'User',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      firstName: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      lastName: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      email: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      countryCode: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      mobileNumber: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      password: {
        type: DataTypes.STRING,
        allowNull: true, // Assuming password is required
      },
      status: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: userStatus.ACTIVE,
      },
      role: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: usersRoles.CUSTOMER,
      },
      tempOtp: { allowNull: true, type: DataTypes.INTEGER },
      tempOtpExpiresAt: { allowNull: true, type: DataTypes.DATE },
      lastSignInAt: { allowNull: true, type: DataTypes.DATE },
      profilePicture: {
        allowNull: true,
        type: DataTypes.TEXT,
        get() {
          return OPTIONS.generateCloudFrontUrl(
            this.getDataValue('profilePicture')
          );
        },
        set(file) {
          if (file) {
            this.setDataValue(
              'profilePicture',
              `uploads/${file.split('uploads/')[1]}`
            );
          }
        },
      },
      googleId: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      appleId: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      facebookId: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      avgRating: {
        type: DataTypes.DECIMAL(3, 2),
        allowNull: false,
        defaultValue: 0.0,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
      hooks: {
        beforeCreate: async (user) => {
          if (user.password) {
            const salt = await bcrypt.genSalt(10);
            user.password = await bcrypt.hash(user.password, salt);
            console.log('beforeCreate user.password', user.password);
          }
        },
        beforeUpdate: async (user) => {
          if (user.changed('password')) {
            const salt = await bcrypt.genSalt(10);
            user.password = await bcrypt.hash(user.password, salt);
          }
        },
      },
      indexes: [
        {
          fields: ['mobileNumber'],
        },
        {
          fields: ['email'],
        },
      ],
      defaultScope: {
        where: {
          // Status is NOT EQUAL (!=) to 'deleted'
          status: { [Op.ne]: userStatus.DELETED },
        },
      },
    }
  );

  // Instance method example for JWT generation (requires jsonwebtoken)
  User.prototype.genToken = function () {
    const payload = {
      id: this.id,
      email: this.email,
      mobileNumber: this.mobileNumber,
      role: this.role,
    };
    // Replace 'YOUR_JWT_SECRET' and options with your config
    return jwt.sign(payload, jwtOptions.secretOrKey, {
      expiresIn: jwtOptions.expiry,
    });
  };

  User.prototype.validPassword = function (password) {
    return this.password ? bcrypt.compareSync(password, this.password) : false;
  };

  User.associate = function (models) {
    User.hasOne(models.BusinessInformation, {
      foreignKey: 'userId',
      as: 'businessInfo',
      onDelete: 'CASCADE',
    });

    User.hasOne(models.ProvidedServiceCancellationPolicy, {
      foreignKey: 'userId',
      as: 'cancellationPolicy',
      onDelete: 'CASCADE',
    });

    User.hasMany(models.ProvidedServiceAvailability, {
      foreignKey: 'userId',
      as: 'providedServiceAvailability',
      onDelete: 'CASCADE',
    });

    User.hasMany(models.Address, {
      foreignKey: 'userId',
      as: 'address',
      onDelete: 'CASCADE',
    });

    User.hasMany(models.ProvidedService, {
      foreignKey: 'userId',
      as: 'providedService',
      onDelete: 'CASCADE',
    });

    User.hasMany(models.AccessManagement, {
      foreignKey: 'userId',
      as: 'accessManagement',
    });

    User.hasMany(models.Vehicle, {
      foreignKey: 'userId',
      as: 'vehicles',
      onDelete: 'CASCADE',
    });
  };

  return User;
};
