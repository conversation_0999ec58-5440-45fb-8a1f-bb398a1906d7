const axios = require('axios');

const commonHeader = {
  Accept: 'application/json',
  'Content-Type': 'application/json;charset=UTF-8',
  'X-Goog-Api-Key': process.env.GOOGLE_MAP_API_KEY,
};

const instance = axios.create();

instance.interceptors.request.use(
  (config) => {
    const newConfig = { ...config };
    newConfig.metadata = { startTime: new Date() };
    return newConfig;
  },
  (error) => {
    return Promise.reject(error);
  }
);
instance.interceptors.response.use(
  (response) => {
    const newRes = { ...response };
    newRes.config.metadata.endTime = new Date();
    newRes.duration =
      newRes.config.metadata.endTime - newRes.config.metadata.startTime;
    return newRes;
  },
  (error) => {
    const newError = { ...error };
    newError.config.metadata.endTime = new Date();
    newError.duration =
      newError.config.metadata.endTime - newError.config.metadata.startTime;
    return Promise.reject(newError);
  }
);
const getTimeBetweenDates = (startDate, endDate) => {
  const milliSeconds = Math.floor(endDate - startDate);
  const seconds = Math.floor((endDate - startDate) / 1000);
  const minutes = Math.floor(seconds / 60);
  return { milliSeconds, seconds, minutes };
};

exports.getCoordinatesByZipcode = async (zipCode) => {
  try {
    console.log('Calling Google API for ge-coding api code ', zipCode);
    const url = `https://maps.googleapis.com/maps/api/geocode/json`;
    const options = {
      method: 'GET',
      url,
      params: {
        address: zipCode,
        key: process.env.GOOGLE_MAP_API_KEY,
        language: 'en',
        region: 'us',
      },
    };
    const response = await instance(options);
    console.info('Google Ge-coding API response with status', response.status);
    console.info(
      'Google Ge-coding API response duration',
      getTimeBetweenDates(
        response.config.metadata.startTime,
        response.config.metadata.endTime
      )
    );
    return response.data;
  } catch (e) {
    console.error('Error Google Ge-coding:', error.message);
    throw e;
  }
};

exports.getPlacesAutoComplete = async (data) => {
  try {
    console.log('Calling Google places api for auto complete', data);
    const url = `https://places.googleapis.com/v1/places:autocomplete`;
    const options = {
      method: 'POST',
      url,
      headers: commonHeader,
      data: {
        input: data.query,
        languageCode: 'en',
        regionCode: 'us',
        includedRegionCodes: ['us'],
        locationBias: {
          circle: {
            center: data.center,
            radius: 30000.0,
          },
        },
      },
    };
    const response = await instance(options);
    console.info(
      'Google places auto complete api response with status',
      response.status
    );
    console.info(
      'Google places auto complete api response duration',
      getTimeBetweenDates(
        response.config.metadata.startTime,
        response.config.metadata.endTime
      )
    );
    return response.data;
  } catch (e) {
    console.error(
      'Google Places API auto complete error',
      JSON.stringify(e.response?.data) || e.message
    );
    throw new Error(
      e.response?.data?.error?.message || 'Unknown Google API Error'
    );
  }
};

// !!ALERT not in use
exports.getPlacesNearBy = async (data) => {
  try {
    console.log('Calling Google places api for search nearby', data);
    const url = `https://places.googleapis.com/v1/places:searchNearby`;
    const options = {
      method: 'POST',
      url,
      headers: {
        ...commonHeader,
        'X-Goog-FieldMask': 'places.id,places.formattedAddress,places.location',
      },
      data: {
        languageCode: 'en',
        maxResultCount: 20,
        rankPreference: 'DISTANCE',
        locationRestriction: {
          circle: {
            center: data.center,
            radius: 30000.0,
          },
        },
        regionCode: 'us',
      },
    };
    const response = await instance(options);
    console.info(
      'Google places api search nearby response with status',
      response.status
    );
    console.info(
      'Google places api response duration',
      getTimeBetweenDates(
        response.config.metadata.startTime,
        response.config.metadata.endTime
      )
    );
    return response.data;
  } catch (e) {
    console.error(
      'Google Places API search nearby error',
      JSON.stringify(e.response?.data) || e.message
    );
    throw new Error(
      e.response?.data?.error?.message || 'Unknown Google API Error'
    );
  }
};
