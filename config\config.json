{"local": {"use_env_variable": "DEV_DATABASE_URL", "logging": false, "sync": false, "dialect": "postgres", "ssl": false}, "development": {"use_env_variable": "DEV_DATABASE_URL", "logging": false, "sync": false, "dialect": "postgres", "ssl": true, "dialectOptions": {"ssl": {"require": true, "rejectUnauthorized": false}}}, "test": {"use_env_variable": "TEST_DATABASE_URL", "logging": false, "sync": false, "dialect": "postgres", "ssl": true, "dialectOptions": {"ssl": {"require": true, "rejectUnauthorized": false}}}, "production": {"use_env_variable": "DATABASE_URL", "logging": false, "sync": false, "dialect": "postgres", "ssl": true, "dialectOptions": {"ssl": {"require": true, "rejectUnauthorized": false}}}}