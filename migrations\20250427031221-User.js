'use strict';

const { userStatus, usersRoles, employmentType } = require('../config/options');

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('User', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      firstName: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      lastName: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      email: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      countryCode: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      mobileNumber: {
        allowNull: true,
        type: Sequelize.STRING,
      },
      password: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      status: {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: userStatus.ACTIVE,
      },
      role: {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: usersRoles.CUSTOMER, // Sensible default
      },
      profilePicture: {
        allowNull: true,
        type: Sequelize.TEXT,
      },
      tempOtp: { allowNull: true, type: Sequelize.INTEGER },
      tempOtpExpiresAt: { allowNull: true, type: Sequelize.DATE },
      lastSignInAt: { allowNull: true, type: Sequelize.DATE },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
    // Add indexes
    await queryInterface.addIndex('User', ['mobileNumber']);
    await queryInterface.addIndex('User', ['email']);
    // Unique index on email is already handled by 'unique: true' above
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('User');
  },
};
