exports.createOrUpdateAddress = {
  zipcode: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'ZIP code cannot be empty',
    },
  },
  latitude: {
    in: ['body'],
    optional: true,
    toFloat: true,
    isFloat: {
      options: { min: -90, max: 90 },
      errorMessage: 'Latitude must be a valid coordinate between -90 and 90',
    },
  },
  longitude: {
    in: ['body'],
    optional: true,
    toFloat: true,
    isFloat: {
      options: { min: -180, max: 180 },
      errorMessage: 'Longitude must be a valid coordinate between -180 and 180',
    },
  },
  city: {
    in: ['body'],
    trim: true,
    optional: true,
  },
  state: {
    in: ['body'],
    trim: true,
    optional: true,
  },
  addressLine1: {
    in: ['body'],
    trim: true,
    optional: true,
  },
  category: {
    in: ['body'],
    trim: true,
    optional: true,
  },
};

exports.searchPlaces = {
  mode: {
    in: ['query'],
    trim: true,
    notEmpty: {
      errorMessage: 'Mode is required',
    },
    isIn: {
      options: [['place']],
      errorMessage: 'Mode must be either "place" ',
    },
    isString: {
      errorMessage: 'Mode must be a string',
    },
  },
  search: {
    in: ['query'],
    trim: true,
    optional: true,
    isString: {
      errorMessage: 'Search must be a string',
    },
  },
  latitude: {
    in: ['query'],
    notEmpty: true,
    toFloat: true,
    isFloat: {
      options: { min: -90, max: 90 },
      errorMessage: 'Latitude must be a valid coordinate between -90 and 90',
    },
  },
  longitude: {
    in: ['query'],
    notEmpty: true,
    toFloat: true,
    isFloat: {
      options: { min: -180, max: 180 },
      errorMessage: 'Longitude must be a valid coordinate between -180 and 180',
    },
  },
};
