const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();

const AddressControl = require('../../../controllers/api/v1/Address');
const AddressSchema = require('../../../schema-validation/Address');
const ErrorHandleHelper = require('../../../models/helpers/ErrorHandleHelper');

router.post(
  '/',
  checkSchema(AddressSchema.createOrUpdateAddress),
  ErrorHandleHelper.requestValidator,
  AddressControl.postAddress
);

router.get(
  '/places',
  checkSchema(AddressSchema.searchPlaces),
  ErrorHandleHelper.requestValidator,
  AddressControl.searchPlaces
);

router.get('/', AddressControl.getAddressListing);

module.exports = router;
