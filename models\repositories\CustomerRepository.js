const {
  usersRoles,
  successMessage,
  errorMessage,
} = require('../../config/options');
const { User, Vehicle, Address } = require('..');
const sequelize = require('sequelize');
const { Op, literal, fn, col, where: sequelizeWhere } = sequelize;
const UserRepository = require('./UserRepository');

const getAllCustomerAndCount = async (payload) => {
  const { start = 0, limit = 10, search, fromDate, toDate } = payload;

  try {
    const whereConditions = [{ role: usersRoles.CUSTOMER }];

    if (search) {
      whereConditions.push({
        [Op.or]: [
          { email: { [Op.iLike]: `%${search}%` } },
          { mobileNumber: { [Op.iLike]: `%${search}%` } },
          { firstName: { [Op.iLike]: `%${search}%` } },
          { lastName: { [Op.iLike]: `%${search}%` } },
          literal(
            `(CONCAT("User"."firstName", ' ', "User"."lastName") ILIKE '%${search}%')`
          ),
        ],
      });
    }

    if (fromDate && toDate) {
      whereConditions.push(
        sequelizeWhere(fn('DATE', col('User.createdAt')), {
          [Op.between]: [fromDate, toDate],
        })
      );
    }

    const { count, rows } = await User.findAndCountAll({
      offset: Number(start),
      limit: Number(limit),
      where: {
        [Op.and]: whereConditions,
      },
      order: [['createdAt', 'DESC']],
      attributes: [
        'id',
        'firstName',
        'lastName',
        [
          literal(`CONCAT("User"."firstName", ' ', "User"."lastName")`),
          'fullName',
        ],
        'email',
        'mobileNumber',
        'profilePicture',
        'status',
        'createdAt',
      ],
    });

    return {
      message: successMessage.DETAIL_MESSAGE('Customer'),
      data: {
        rows,
        pagination: {
          totalCount: count,
          start: Number(start),
          limit: Number(limit),
        },
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndGetCustomerById = async (customerId) => {
  try {
    const existingCustomer = await User.findOne({
      where: {
        id: customerId,
        role: usersRoles.CUSTOMER,
      },
      attributes: [
        'id',
        'firstName',
        'lastName',
        [
          literal(`CONCAT("User"."firstName", ' ', "User"."lastName")`),
          'fullName',
        ],
        'email',
        'mobileNumber',
        'profilePicture',
      ],
      include: [
        {
          model: Address,
          as: 'address',
          attributes: [
            'id',
            'zipcode',
            'state',
            'city',
            'radius',
            'addressType',
          ],
          required: false,
        },
        {
          model: Vehicle,
          as: 'vehicles',
          attributes: [
            'id',
            'vehicleType',
            'year',
            'industry',
            'model',
            'vehicleTrim',
            'engine',
            'driveTrain',
            'milage',
          ],
          required: false,
        },
      ],
    });

    if (!existingCustomer) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Customer'),
      };
    }

    return {
      success: true,
      data: existingCustomer,
      message: successMessage.DETAIL_MESSAGE('Customer'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndPatchCustomerStatus = async (id, isDelete) => {
  try {
    const { success, message } = await UserRepository.checkAndUpdateStatus(
      id,
      isDelete
    );
    return {
      success,
      message,
    };
  } catch (error) {
    throw new Error(error);
  }
};

module.exports = {
  getAllCustomerAndCount,
  checkAndGetCustomerById,
  checkAndPatchCustomerStatus,
};
