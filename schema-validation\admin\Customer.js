const customerDateQueryValidation = {
  fromDate: {
    in: ['query'],
    optional: true,
    isISO8601: {
      errorMessage: 'fromDate must be a valid ISO date (YYYY-MM-DD)',
    },
  },
  toDate: {
    in: ['query'],
    optional: true,
    isISO8601: {
      errorMessage: 'toDate must be a valid ISO date (YYYY-MM-DD)',
    },
    custom: {
      options: (toDate, { req }) => {
        const fromDate = req.query.fromDate;
        if (fromDate && toDate && new Date(fromDate) > new Date(toDate)) {
          throw new Error('fromDate cannot be after toDate');
        }
        return true;
      },
    },
  },
};

module.exports = {
  customerDateQueryValidation,
};
