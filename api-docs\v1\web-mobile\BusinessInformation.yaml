paths:
  /business-info:
    post:
      tags:
        - "BusinessInformation"
      summary: "Create Business information for Provider"
      description: "Allows a logged-in user to create business information"
      operationId: "createBusinessInformation"
      security:
        - bearerAuth: [ ]
      requestBody:
        description: "Payload with business info details"
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createOrUpdateBusinessInformation"
      produces:
        - "application/json"
      responses:
        "201":
          description: "Business information created successfully"
        "400":
          description: "Invalid input"
        "500":
          description: "Internal Server Error"

    get:
      tags:
        - "BusinessInformation"
      summary: "Get Business Information details by ID"
      description: "Fetch a single Business Information details using its ID."
      operationId: "getBusinessInformation"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Business Information details retrieved successfully"
        "400":
          description: "Invalid Business Information ID"
        "404":
          description: "Business Information not found"
        "500":
          description: "Internal Server Error"

  /business-info/{id}:
    put:
      tags:
        - "BusinessInformation"
      summary: "Update an existing Business information"
      description: "Allows a logged-in user to update an existing Business information by ID"
      operationId: "updateBusinessInformation"
      security:
        - bearerAuth: [ ]
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the Business information to update
          schema:
            type: integer
      requestBody:
        description: "Payload with updated Business information details"
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createOrUpdateBusinessInformation"
      produces:
        - "application/json"
      responses:
        "200":
          description: "Business information updated successfully"
        "400":
          description: "Invalid input or Business information not found"
        "500":
          description: "Internal Server Error"

components:
  schemas:
    createOrUpdateBusinessInformation:
      type: object
      properties:
        businessLogo:
          type: string
          example: "uploads/images/business-logo.png"
        businessName:
          type: string
          example: "Expert Plumbing Co."
        about:
          type: string
          example: "All car services at one place"
        yearOfExperience:
          type: integer
          example: 5
        hasCertificationLicense:
          type: boolean
          example: true
        isInsured:
          type: boolean
          example: true
        hasToolset:
          type: boolean
          example: false
      required:
        - yearOfExperience