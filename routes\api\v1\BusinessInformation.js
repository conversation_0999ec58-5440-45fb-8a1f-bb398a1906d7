const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();

const BusinessInfoControl = require('../../../controllers/api/v1/BusinessInformation');
const BusinessInfoSchema = require('../../../schema-validation/BusinessInformation');
const BusinessDocumentControl = require('../../../controllers/api/v1/BusinessDocument');
const BusinessDocumentSchema = require('../../../schema-validation/BusinessDocument');
const ErrorHandleHelper = require('../../../models/helpers/ErrorHandleHelper');

router.post(
  '/',
  checkSchema(BusinessInfoSchema.createOrUpdateBusinessInfo),
  ErrorHandleHelper.requestValidator,
  BusinessInfoControl.postBusinessInfo
);

router.put(
  '/:id',
  checkSchema(BusinessInfoSchema.createOrUpdateBusinessInfo),
  ErrorHandleHelper.requestValidator,
  BusinessInfoControl.putBusinessInfo
);

router.get('/', BusinessInfoControl.getBusinessInfo);

router.post(
  '/document',
  checkSchema(BusinessDocumentSchema.addBusinessDocuments),
  ErrorHandleHelper.requestValidator,
  BusinessDocumentControl.postBusinessDocument
);

router.put(
  '/document/data',
  checkSchema(BusinessDocumentSchema.updateBusinessDocuments),
  ErrorHandleHelper.requestValidator,
  BusinessDocumentControl.putBusinessDocument
);

router.get('/document', BusinessDocumentControl.getBusinessDocument);

module.exports = router;
