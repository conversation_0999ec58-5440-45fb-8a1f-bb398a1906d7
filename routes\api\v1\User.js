const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();

const AuthHandler = require('../../../models/helpers/AuthHelper');
const UserControl = require('../../../controllers/api/v1/User');
const UserSchema = require('../../../schema-validation/User');
const ErrorHandleHelper = require('../../../models/helpers/ErrorHandleHelper');

router.post(
  '/login',
  checkSchema(UserSchema.sendOtp),
  ErrorHandleHelper.requestValidator,
  UserControl.sendLoginOtp
);

router.patch(
  '/verify-otp',
  checkSchema(UserSchema.verifyOtp),
  ErrorHandleHelper.requestValidator,
  UserControl.verifyLoginOtp
);

router.post(
  '/send-otp',
  AuthHandler.authenticateJWT(),
  checkSchema(UserSchema.sendOtp),
  ErrorHandleHelper.requestValidator,
  UserControl.sendOtpToMobileAndEmailChange
);

router.patch(
  '/verify-otp-email-phone-change',
  AuthHandler.authenticateJWT(),
  checkSchema(UserSchema.verifyOtp),
  ErrorHandleHelper.requestValidator,
  UserControl.verifyOtpForEmailAndMobileChange
);

router.put(
  '/',
  AuthHandler.authenticateJWT(),
  checkSchema(UserSchema.updateInfo),
  ErrorHandleHelper.requestValidator,
  UserControl.putUserProfile
);

router.get('/', AuthHandler.authenticateJWT(), UserControl.getUserProfile);

router.patch(
  '/close-account',
  AuthHandler.authenticateJWT(),
  UserControl.deleteUserAccount
);

router.post(
  '/signup-phone',
  checkSchema(UserSchema.signUp),
  ErrorHandleHelper.requestValidator,
  UserControl.signupPhone
);

router.post(
  '/verify-phone',
  checkSchema(UserSchema.verifyOtp),
  ErrorHandleHelper.requestValidator,
  UserControl.verifySignPhone
);

router.post(
  '/sso',
  checkSchema(UserSchema.sso),
  ErrorHandleHelper.requestValidator,
  UserControl.loginWitSSO
);

module.exports = router;
