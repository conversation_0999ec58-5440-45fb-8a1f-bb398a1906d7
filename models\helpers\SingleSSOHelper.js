const axios = require('axios');
const { verifyIdToken } = require('apple-signin-auth');

const { errorMessage } = require('../../config/options');

exports.checkGoogleToken = async ({ idToken, googleId }) => {
  try {
    const appLink = `https://oauth2.googleapis.com/tokeninfo?id_token=${idToken}`;
    const response = await axios.get(appLink);
    const data = response.data;
    if (!data) {
      return {
        success: false,
        message: errorMessage.INVALID_REQUEST,
      };
    } else if (googleId !== data['sub']) {
      return {
        success: false,
        message: errorMessage.INVALID_REQUEST,
      };
    }
    return {
      success: true,
      message: 'Logged in token correct',
      data,
    };
  } catch (error) {
    return {
      success: false,
      message: errorMessage.INVALID_REQUEST,
    };
  }
};

exports.checkAppleToken = async ({ idToken, appleId }) => {
  try {
    const response = await verifyIdToken(idToken);
    const data = response.data;
    if (!data) {
      return {
        success: false,
        message: errorMessage.INVALID_REQUEST,
      };
    } else if (appleId !== data['sub']) {
      return {
        success: false,
        message: errorMessage.INVALID_REQUEST,
      };
    }
    return {
      success: true,
      message: 'Logged in token correct',
      data,
    };
  } catch (error) {
    return {
      success: false,
      message: errorMessage.INVALID_REQUEST,
    };
  }
};

exports.checkFacebookToken = async ({ idToken, facebookId }) => {
  try {
    const appId = process.env.FACEBOOK_APP_ID;
    const appSecret = process.env.FACEBOOK_APP_SECRET;

    // Facebook app access token = app_id|app_secret
    const appAccessToken = `${appId}|${appSecret}`;

    // Step 1: Verify the access token is valid and belongs to your app
    const url = `https://graph.facebook.com/debug_token?input_token=${idToken}&access_token=${appAccessToken}`;
    const response = await axios.get(url);
    const data = response.data?.data;

    if (!data || !data.is_valid) {
      return {
        success: false,
        message: errorMessage.INVALID_REQUEST,
      };
    }

    if (data.app_id !== appId) {
      return {
        success: false,
        message: 'Access token does not belong to this app.',
      };
    }

    if (data.user_id !== facebookId) {
      return {
        success: false,
        message: 'Token user ID does not match provided Facebook ID.',
      };
    }
    const userInfoRes = await axios.get(
      `https://graph.facebook.com/me?fields=id,name,email&access_token=${idToken}`
    );
    const user = userInfoRes.data;
    if (!data) {
      return {
        success: false,
        message: errorMessage.INVALID_REQUEST,
      };
    }
    return {
      success: true,
      message: 'Facebook token is valid',
      data: user,
    };
  } catch (error) {
    return {
      success: false,
      message: errorMessage.INVALID_REQUEST,
    };
  }
};
