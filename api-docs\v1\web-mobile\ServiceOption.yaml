paths:
  /service-option:
    get:
      tags:
        - "ServiceOption"
      summary: "Get list of Service option with pagination and search"
      description: "Fetch paginated list of Service option with optional search filter."
      operationId: "listServiceOption"
      parameters:
        - name: "serviceTypeId"
          in: "query"
          required: true
          description: "Service Type ID"
          schema:
            type: string
            format: uuid
        - name: "subServiceId"
          in: "query"
          required: true
          description: "Sub service ID"
          schema:
            type: string
            format: uuid
        - name: "start"
          in: "query"
          description: "Starting index for pagination"
          required: false
          schema:
            type: integer
            default: 0
        - name: "limit"
          in: "query"
          description: "Number of records to return"
          required: false
          schema:
            type: integer
            default: 10
        - name: "search"
          in: "query"
          description: "Search keyword to filter types by name"
          required: false
          schema:
            type: string
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "List of Service option fetched successfully."
        "400":
          description: "Invalid request parameters."
        "500":
          description: "Internal server error."