NODE_ENV=development
PORT=3000

DEV_DATABASE_URL=postgres://postgres:password@localhost:5432/revilo-core-dev

SESSION_SECRET=backend#124
JWT_SECRET_KEY=

AWS_S3_REGION=ap-south-1

AWS_INPUT_BUCKET=revilo-core-assets-dev
AWS_ACCESS_KEY=
AWS_SECRET_KEY=
CDN_WEB_STATIC=

SWAGGER_ADMIN_USER=<EMAIL>
SWAGGER_ADMIN_PASSWORD=revilo@revilo

SWAGGER_MOBILE_USER=<EMAIL>
SWAGGER_MOBILE_PASSWORD=revilo@revilo

FRONTEND_URL=http://localhost:4200
BACKEND_URL=http://localhost:$PORT

FACEBOOK_APP_ID=
FACEBOOK_APP_SECRET=

GOOGLE_CLIENT_ID=
GOOGLE_API_KEY=

SMS_ACCOUNT_SID=
SMS_AUTH_TOKEN=
SMS_FROM=

GOOGLE_MAP_API_KEY=

