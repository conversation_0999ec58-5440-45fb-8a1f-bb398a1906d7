'use strict';

const { serviceStatus, serviceRequestStatus } = require('../config/options');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('ServiceRequest', 'rescheduleReason', {
      type: Sequelize.TEXT,
      allowNull: true,
    });
    await queryInterface.addColumn('ServiceRequest', 'cancellationReason', {
      type: Sequelize.STRING,
      allowNull: true,
    });
    await queryInterface.changeColumn('ServiceRequest', 'status', {
      type: Sequelize.STRING,
      allowNull: true,
      defaultValue: serviceRequestStatus.PENDING,
    });
    await queryInterface.addColumn('ServiceRequest', 'serviceStartedAt', {
      type: Sequelize.DATE,
      allowNull: true,
    });
    await queryInterface.addColumn('ServiceRequest', 'serviceCompletedAt', {
      type: Sequelize.DATE,
      allowNull: true,
    });
    await queryInterface.addColumn('ServiceRequest', 'cancelledById', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'User',
        key: 'id',
      },
      onDelete: 'CASCADE',
    });
    await queryInterface.addColumn('ServiceRequest', 'rescheduledById', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'User',
        key: 'id',
      },
      onDelete: 'CASCADE',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('ServiceRequest', 'rescheduleReason');
    await queryInterface.removeColumn('ServiceRequest', 'cancellationReason');
    await queryInterface.changeColumn('ServiceRequest', 'status', {
      type: Sequelize.STRING,
      allowNull: true,
      defaultValue: serviceStatus.ACTIVE,
    });
    await queryInterface.removeColumn('ServiceRequest', 'serviceStartedAt');
    await queryInterface.removeColumn('ServiceRequest', 'serviceCompletedAt');
    await queryInterface.removeColumn('ServiceRequest', 'cancelledById');
    await queryInterface.removeColumn('ServiceRequest', 'rescheduledById');
  },
};
