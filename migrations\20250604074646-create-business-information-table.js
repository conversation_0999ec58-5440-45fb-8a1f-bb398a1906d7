'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('BusinessInformation', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'User',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      businessLogo: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      businessName: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      yearOfExperience: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      hasCertificationLicense: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
      },
      isInsured: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
      },
      hasToolset: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('BusinessInformation');
  },
};
