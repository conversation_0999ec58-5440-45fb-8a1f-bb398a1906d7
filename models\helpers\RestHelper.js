const axios = require('axios');
const { customErrorLogger } = require('./ErrorHandleHelper');

const commonHeader = {
  Accept: 'application/json',
  'Content-Type': 'application/json;charset=UTF-8',
};

exports.get = async (url, data, headers) => {
  try {
    const token = headers.authorization.split(' ')[1];
    if (token) {
      commonHeader.Authorization = `Bearer ${token.replace('Bearer ', '')}`;
    }
    const options = {
      method: 'GET',
      url,
      headers: commonHeader,
      params: data,
    };
    console.log('option', options);
    const response = await axios(options);
    return response.data;
  } catch (e) {
    console.log('error response', e.response.data);
    customErrorLogger(e.response);
  }
};

exports.post = async (url, data, headers) => {
  try {
    const token = headers.authorization.split(' ')[1];
    if (token) {
      commonHeader.Authorization = `Bearer ${token.replace('Bearer ', '')}`;
    }
    const options = {
      method: 'POST',
      url,
      headers: commonHeader,
      data,
    };
    const response = await axios(options);
    return response;
  } catch (error) {
    console.log('error response', error);
    return { data: error.response.data, status: error.response.status };
  }
};
exports.put = async (url, data, headers) => {
  try {
    const token = headers.authorization.split(' ')[1];
    const deviceId = headers.deviceid;
    if (token) {
      commonHeader.Authorization = `JWT ${token.replace('JWT ', '')}`;
    }
    if (deviceId) {
      commonHeader.deviceId = deviceId;
    }
    const options = {
      method: 'PUT',
      url,
      headers: commonHeader,
      data,
    };
    const response = await axios(options);
    const responseOK =
      response && (response.status === 200 || response.status === 400);
    if (responseOK) {
      return await response.data;
    }
    return new Error('Internal server error.');
  } catch (e) {
    customErrorLogger(e);
  }
};
exports.patch = async (url, data, headers) => {
  try {
    const token = headers.authorization.split(' ')[1];
    const deviceId = headers.deviceid;
    if (token) {
      commonHeader.Authorization = `JWT ${token.replace('JWT ', '')}`;
    }
    if (deviceId) {
      commonHeader.deviceId = deviceId;
    }
    const options = {
      method: 'PATCH',
      url,
      headers: commonHeader,
      data,
    };
    const response = await axios(options);
    const responseOK =
      response && (response.status === 200 || response.status === 400);
    if (responseOK) {
      return await response.data;
    }
    return new Error('Internal server error.');
  } catch (e) {
    customErrorLogger(e);
  }
};
exports.getBase64FromURL = async (data) => {
  try {
    const response = await axios.get(data.URL, {
      responseType: 'arraybuffer',
    });
    return Buffer.from(response.data, 'binary').toString('base64');
  } catch (e) {
    customErrorLogger(e);
  }
};
