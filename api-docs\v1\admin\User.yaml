paths:
  /admin/user/login:
    post:
      tags:
        - "User"
      summary: "Log in User"
      description: "Allows an admin user to log in using their email and password."
      operationId: "userLogin"
      requestBody:
        description: "Admin login using email and password."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/adminLogin"
        required: true
      produces:
        - "application/json"
      parameters: []
      responses:
        "200":
          description: "Logged in successfully."
        "400":
          description: "Invalid request. Please check the input data."
        "500":
          description: "Internal server error. Please try again later."
  /admin/user:
    get:
      tags:
        - "User"
      summary: "Get User Profile by ID"
      description: "Retrieve the profile of a user by their unique ID."
      operationId: "getUserProfileById"
      produces:
        - "application/json"
      parameters: []
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Successfully retrieved the user profile."
        "400":
          description: "Invalid request. Please check the input data."
        "404":
          description: "User not found."
        "500":
          description: "Internal server error. Please try again later."
    put:
      tags:
        - "User"
      summary: "Update Profile"
      operationId: "updateProfile"
      requestBody:
        description: Payload is required
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/updateProfile"
        required: true
      produces:
        - "application/json"
      parameters: []
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: "User profile updated successfully"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"
    
  /admin/user/send-otp:
    post:
      tags:
        - "User"
      summary: "Send OTP to Email"
      description: "Allows an admin user to request an OTP to be sent to their email for verification purposes."
      operationId: "sendOtp"
      requestBody:
        description: "Payload containing the email address to send the OTP."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/sendOtpEmail"
        required: true
      produces:
        - "application/json"
      parameters: []
      responses:
        "200":
          description: "An OTP has been sent to the provided email address."
        "400":
          description: "Invalid request. No user found with the provided email address."
        "500":
          description: "Internal server error. Please try again later."
  /admin/user/password:
    patch:
      tags:
        - "User"
      summary: "Verify OTP and Update Password"
      description: "Allows an admin user to verify the OTP sent to their email and update their password."
      operationId: "updatePassword"
      requestBody:
        description: "Payload containing the email, OTP, and new password."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/updatePasswordWithEmailOtp"
        required: true
      produces:
        - "application/json"
      parameters: []
      responses:
        "200":
          description: "Password updated successfully."
        "400":
          description: "Invalid request or incorrect OTP provided."
        "500":
          description: "Internal server error. Please try again later."
        
components:
  schemas:
    adminLogin:
      type: object
      properties:
        email:
          type: string
          description: "Enter the email address of the admin user."
          example: "<EMAIL>"
        password:
          type: string
          description: "Enter the password of the admin user."
          example: "password123"
    sendOtpEmail:
      type: object
      properties:
        email:
          type: string
          description: enter email
      required:
        - email
    updatePasswordWithEmailOtp:
      type: object
      properties:
        email:
          type: string
          description: enter email
        tempOtp:
          type: integer
          description: enter otp       
        password:
          type: string
          description: enter password
        confirmPassword:
          type: string
          description: enter confirm password
      required:
        - email
        - tempOtp
        - password
        - confirmPassword
    updateProfile:
      type: object
      properties:
        firstName:
          type: string
          description: "Enter the first name of the Sub-Admin."
        lastName:
          type: string
          description: "Enter the last name of the Sub-Admin."
        email:
          type: string
          description: "Enter the email address of the Sub-Admin."
        countryCode:
          type: string
          description: "Enter the country code of mobile number of the Sub-Admin."
          default: 91
        mobileNumber:
          type: string
          description: "Enter the mobile number of the Sub-Admin."
        profilePicture:
          type: string
          description: "Provide the URL of the Sub-Admin's profile picture."
      required:
        - firstName
        - lastName
        - email
        - countryCode
        - mobileNumber