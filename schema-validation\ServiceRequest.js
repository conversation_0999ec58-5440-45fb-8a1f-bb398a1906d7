const {
  propertyLocation,
  addressType,
  serviceRequestProviderStatus,
  serviceAppointmentUpdateType,
} = require('../config/options');

exports.addServiceRequest = {
  vehicleId: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'vehicleId is required',
    },
    isInt: {
      errorMessage: 'vehicleId must be an integer',
    },
  },
  scheduledAt: {
    in: ['body'],
    optional: true,
    isISO8601: {
      errorMessage: 'scheduledAt must be a valid ISO8601 date',
    },
  },
  propertyLocation: {
    in: ['body'],
    optional: true,
    trim: true,
    isIn: {
      options: [Object.values(propertyLocation)],
      errorMessage: `propertyLocation must be one of: ${Object.values(propertyLocation).join(', ')}`,
    },
  },
  otherLocation: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'otherLocation must be a string',
    },
  },
  specialInstruction: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'specialInstruction must be a string',
    },
  },
  note: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'note must be a string',
    },
  },
  serviceDuration: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'serviceDuration must be a string',
    },
  },
  serviceOptionIds: {
    in: ['body'],
    optional: true,
    isArray: {
      errorMessage: 'serviceOptionIds must be an array',
    },
  },
  'serviceOptionIds.*': {
    in: ['body'],
    isUUID: {
      errorMessage: 'Each serviceOptionId must be a valid UUID',
    },
  },

  providerIds: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'providerIds is required',
    },
    isArray: {
      errorMessage: 'providerIds must be an array',
    },
  },

  'providerIds.*': {
    in: ['body'],
    isInt: {
      errorMessage: 'Each providerId must be an integer',
    },
  },

  addressId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'addressId must be an integer',
    },
    custom: {
      options: (value, { req }) => {
        const hasAddressId = !!value;
        const hasAddressZipcode = !!req.body.address?.zipcode;
        if (!hasAddressId && !hasAddressZipcode) {
          throw new Error('Either addressId or address.zipcode is required');
        }
        return true;
      },
    },
  },

  'address.zipcode': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'zipcode must be a string',
    },
    trim: true,
  },
  'address.city': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'city must be a string',
    },
    trim: true,
  },
  'address.state': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'state must be a string',
    },
    trim: true,
  },
  'address.addressType': {
    in: ['body'],
    optional: true,
    isIn: {
      options: [Object.values(addressType)],
      errorMessage: `addressType must be one of: ${Object.values(addressType).join(', ')}`,
    },
  },
};

exports.updateLeadStatus = {
  requestStatus: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Status is required',
    },
    isIn: {
      options: [
        [
          serviceRequestProviderStatus.ACCEPTED,
          serviceRequestProviderStatus.REJECTED,
        ],
      ],
      errorMessage: `Status must be one of ${
        (serviceRequestProviderStatus.ACCEPTED,
        serviceRequestProviderStatus.REJECTED)
      }`,
    },
  },
};

exports.updateAppointment = {
  operation: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'operation is required',
    },
    isIn: {
      options: [Object.values(serviceAppointmentUpdateType)],
      errorMessage: `operation must be one of ${Object.values(serviceAppointmentUpdateType).join(', ')}`,
    },
  },
  cancellationReason: {
    in: ['body'],
    notEmpty: {
      errorMessage: `cancellationReason is required when type is ${serviceAppointmentUpdateType.CANCEL}`,
      if: (value, { req }) =>
        req.body.type === serviceAppointmentUpdateType.CANCEL,
      else: (value) => false,
    },
    isString: {
      errorMessage: 'cancellationReason must be a string',
    },
  },
  rescheduleReason: {
    in: ['body'],
    notEmpty: {
      errorMessage: `rescheduleReason is required when type is ${serviceAppointmentUpdateType.RESCHEDULE}`,
      if: (value, { req }) =>
        req.body.type === serviceAppointmentUpdateType.RESCHEDULE,
      else: (value) => false,
    },
    isString: {
      errorMessage: 'rescheduleReason must be a string',
    },
  },
  scheduledAt: {
    in: ['body'],
    notEmpty: {
      errorMessage: `scheduledAt is required when type is ${serviceAppointmentUpdateType.RESCHEDULE}`,
      if: (value, { req }) =>
        req.body.type === serviceAppointmentUpdateType.RESCHEDULE,
      else: (value) => false,
    },
    isISO8601: {
      errorMessage: 'scheduledAt must be a valid ISO8601 date',
    },
  },
};
