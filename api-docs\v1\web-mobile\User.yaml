paths:
  /user/login:
    post:
      tags:
        - "User"
      summary: "Login with mobileNumber and Email"
      description: "Allows users to log in using either their mobile number or email address which will send otp to registered user"
      operationId: "loginWithEmailMobileNumber"
      requestBody:
        description:  Login with Email & MobileNumber
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/send-otp"
        required: true
      produces:
        - "application/json"
      parameters: []
      responses:
        "200":
          description: "Logged in successfully"
        "400":
          description: "Invalid Request | No user with this Email or Mobile number"
        "500":
          description: "Internal Server Error"
  /user/verify-otp:
    patch:
      tags:
        - "User"
      summary: "Verify otp with Email and mobileNumber"
      description: "Verifies the one-time password (OTP) sent to the user's mobile number or email address. Ensures the provided OTP matches the one generated for the given contact information, allowing the user to proceed with authentication or account verification."
      operationId: "verifyOtpEmailMobileNumber"
      requestBody:
        description: verify otp with Email MobileNumber
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/verify-otp"
        required: true
      produces:
        - "application/json"
      parameters: [ ]
      responses:
        "200":
          description: "OTP verified successfully"
        "400":
          description: "Invalid Request or Incorrect OTP"
        "500":
          description: "Internal Server Error"
  /user/send-otp:
    post:
      tags:
        - "User"
      summary: "send otp to change email or mobile number"
      description: "send otp to change email or mobile number for loggedin user"
      operationId: "sendOtpToMobileAndEmailChange"
      requestBody:
        description:  send otp to change email or mobile number for loggedin user
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/send-otp"
        required: true
      produces:
        - "application/json"
      parameters: []
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: "An OTP has been sent"
        "400":
          description: "Invalid Request | No user with this Email or mobile number"
        "500":
          description: "Internal Server Error"
  /user/verify-otp-email-phone-change:
    patch:
      tags:
        - "User"
      summary: "Verify otp to change email and mobile number"
      description: "Verify otp to change email and mobile number for LoggedIn user"
      operationId: "verifyOtpForEmailAndMobileChange"
      requestBody:
        description: verify otp change email and mobile number for LoggedIn user
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/verify-otp"
        required: true
      produces:
        - "application/json"
      parameters: [ ]
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: "OTP verified successfully"
        "400":
          description: "Invalid Request or Incorrect OTP"
        "500":
          description: "Internal Server Error"
  /user:
    put:
      tags:
        - "User"
      summary: "update profile"
      description: "Update user profile with the loggedin user"
      operationId: "update profile"
      requestBody:
        description: Payload required
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/profile-update"
        required: true
      produces:
        - "application/json"
      parameters: []  
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: "user updated successfully"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"
    get:
     tags:
       - "User"
     summary: "get profile by id"
     description: "Get user profile by loggedin user"
     operationId: "get user profile"
     produces:
       - "application/json"
     parameters: [ ]
     security:
       - bearerAuth: [ ]
     responses:
       "200":
         description: "get user profile "
       "400":
         description: "Invalid Request"
       "500":
         description: "Internal Server Error"
  /user/close-account:
    patch:
      tags:
        - "User"
      summary: "Delete User Account"
      operationId: deleteUserAccount
      produces:
        - "application/json"
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: "User Account Deleted successfully"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"

  /user/signup-phone:
    post:
      tags:
        - "User"
      summary: "Signup using phone number"
      description: "Sign up user with mobileNumber"
      operationId: "signupPhone"
      requestBody:
        description: "Payload required"
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/sign-up"
        required: true
      produces:
        - "application/json"
      parameters: []
      responses:
        "200":
          description: "Signup successful"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"

  /user/verify-phone:
    post:
      tags:
        - "User"
      summary: "Verify phone number using OTP"
      description: "Verify signup Phone with otp"
      operationId: "verifyPhone"
      requestBody:
        description: "Payload required"
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/verify-otp"
        required: true
      produces:
        - "application/json"
      parameters: []
      responses:
        "200":
          description: "Phone verified successfully"
        "400":
          description: "Invalid OTP or user"
        "500":
          description: "Internal Server Error"

  /user/sso:
    post:
      tags:
        - "User"
      summary: "Login or signup with SSO (Google, Apple, Facebook)"
      description: "Authenticate or register a user using a supported SSO provider."
      operationId: "loginWithSSO"
      requestBody:
        description: "Payload for SSO login/signup. Only the relevant provider ID is required."
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/sso-login"
      produces:
        - "application/json"
      parameters: []
      responses:
        "200":
          description: "Logged in or signed up successfully via SSO"
        "400":
          description: "Invalid Request | SSO provider or ID missing/invalid"
        "500":
          description: "Internal Server Error"
    
components:
  schemas:
    send-otp:
      type: object
      properties:
        type:
          type: string
          description: enter type
          enum: ['email', 'mobileNumber']
        countryCode:
          type: string
          description: enter country code
        mobileNumber:
          type: string
          description: enter mobile Number
        email:
          type: string
          description: enter email
        role:
          type: string
          description: User role
          enum: [CUSTOMER, PROVIDER]
          example: CUSTOMER
      required:
        - type
        - role

    verify-otp:
      type: object
      properties:
        type:
          type: string
          description: enter type
          enum: ['email', 'mobileNumber']
          example: mobileNumber
        countryCode:
          type: string
          description: enter country code
        mobileNumber:
          type: string
          description: enter mobile Number
        email:
          type: string
          description: enter email
        tempOtp:
          type: integer
          description: enter otp
        role:
          type: string
          description: User role
          enum: [CUSTOMER, PROVIDER]
          example: CUSTOMER
      required:
        - tempOtp
        - type
        - role
      
    sign-up:
      type: object
      properties:
        countryCode:
          type: string
          description: enter country code
        mobileNumber:
          type: string
          description: enter mobile Number
        firstName:
          type: string
          description: enter first name        
        lastName:
          type: string
          description: enter last name 
        email:
          type: string
          description: enter last name 
        role:
          type: string
          example: "CUSTOMER"
          description: "User role (set internally)"  

    profile-update:
      type: object
      properties:
        firstName:
          type: string
          description: enter first name        
        lastName:
          type: string
          description: enter last name
        profilePicture:
          type: string
          description: enter profilePicture
        email:
          type: string
          description: enter email

    sso-login:
      type: object
      properties:
        ssoProvider:
          type: string
          description: SSO provider
          enum: [google, apple, facebook]
          example: google
        googleId:
          type: string
          description: Google user ID (required if ssoProvider is google)
        appleId:
          type: string
          description: Apple user ID (required if ssoProvider is apple)
        facebookId:
          type: string
          description: Facebook user ID (required if ssoProvider is facebook)
        idToken:
          type: string
          description: Id Token of the sso provider
        firstName:
          type: string
          description: First name of the sso provider
        lastName:
          type: string
          description: Last name of the sso provider
        role:
          type: string
          description: User role
          enum: [CUSTOMER, PROVIDER]
          example: CUSTOMER
      required:
        - ssoProvider
        - role
        - idToken
