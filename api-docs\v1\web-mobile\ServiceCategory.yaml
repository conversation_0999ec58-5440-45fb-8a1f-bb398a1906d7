paths:
  /service-category:
    get:
      tags:
        - "ServiceCategory"
      summary: "Get list of Service Categories with pagination and search"
      description: "Fetch paginated list of Service Categories with optional search filter."
      operationId: "listServiceCategories"
      parameters:
        - name: "serviceId"
          in: "query"
          description: "filter by service id"
          required: false
          schema:
            type: integer
            default: 0
        - name: "start"
          in: "query"
          description: "Starting index for pagination"
          required: false
          schema:
            type: integer
            default: 0
        - name: "limit"
          in: "query"
          description: "Number of records to return"
          required: false
          schema:
            type: integer
            default: 10
        - name: "search"
          in: "query"
          description: "Search keyword to filter categories by name or status"
          required: false
          schema:
            type: string
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "List of Service Categories fetched successfully."
        "400":
          description: "Invalid request parameters."
        "500":
          description: "Internal server error."