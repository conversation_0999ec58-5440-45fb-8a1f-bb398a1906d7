'use strict';

const { serviceStatus, generateCloudFrontUrl } = require('../config/options');

const { Op } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  const Service = sequelize.define(
    'Service',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      imageUrl: {
        allowNull: false,
        type: DataTypes.TEXT,
        get() {
          return generateCloudFrontUrl(this.getDataValue('imageUrl'));
        },
        set(file) {
          if (file) {
            this.setDataValue(
              'imageUrl',
              `uploads/${file.split('uploads/')[1]}`
            );
          }
        },
      },
      bannerUrl: {
        allowNull: false,
        type: DataTypes.TEXT,
        get() {
          return generateCloudFrontUrl(this.getDataValue('bannerUrl'));
        },
        set(file) {
          if (file) {
            this.setDataValue(
              'bannerUrl',
              `uploads/${file.split('uploads/')[1]}`
            );
          }
        },
      },
      status: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: serviceStatus.ACTIVE,
      },
    },
    {
      tableName: 'Service',
      timestamps: true,
      defaultScope: {
        where: {
          status: {
            [Op.ne]: serviceStatus.DELETED,
          },
        },
      },
    }
  );

  Service.associate = function (models) {
    Service.hasMany(models.ServiceCategory, {
      foreignKey: 'serviceId',
      as: 'serviceCategory',
      onDelete: 'CASCADE',
    });
  };

  return Service;
};
