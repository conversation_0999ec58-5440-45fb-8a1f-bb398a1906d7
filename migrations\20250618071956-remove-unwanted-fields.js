'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('ServiceRequest', 'serviceOptionId');
    await queryInterface.removeColumn('ServiceRequest', 'note');
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('ServiceRequest', 'serviceOptionId', {
      type: Sequelize.UUID,
      allowNull: true,
      references: {
        model: 'ServiceOption',
        key: 'id',
      },
      onDelete: 'CASCADE',
    });

    await queryInterface.addColumn('ServiceRequest', 'note', {
      type: Sequelize.TEXT,
      allowNull: true,
    });
  },
};
