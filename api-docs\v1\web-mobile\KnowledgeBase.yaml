paths:
  /knowledge-base:
    get:
      tags:
        - "KnowledgeBase"
      summary: "Get a list of Knowledge Base entries"
      description: "Fetch a paginated list of Knowledge Base entries. Supports search and pagination."
      operationId: "getKnowledgeBaseList"
      parameters:
        - name: start
          in: query
          required: false
          schema:
            type: integer
            default: 0
          description: "Pagination offset (default: 0)"
        - name: limit
          in: query
          required: false
          schema:
            type: integer
            default: 10
          description: "Number of entries to return (default: 10)"
        - name: userType
          in: query
          required: true
          schema:
            type: string
            enum: [PROVIDER, CUSTOMER]
            default: CUSTOMER
          description: "Filter by user type (default: CUSTOMER)"
      responses:
        "200":
          description: "List of Knowledge Base entries fetched successfully."
        "500":
          description: "Internal server error."