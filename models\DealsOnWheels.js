const OPTIONS = require('../config/options');
const { Op } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  const DealsOnWheels = sequelize.define(
    'DealsOnWheels',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      status: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: OPTIONS.dealsOnWheelsStatus.ACTIVE,
      },
      posterImage: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      redirectionUrl: {
        type: DataTypes.STRING,
        allowNull: false,
      },
    },
    {
      tableName: 'DealsOnWheels',
      timestamps: true,
      defaultScope: {
        where: {
          status: {
            [Op.eq]: OPTIONS.dealsOnWheelsStatus.ACTIVE,
            [Op.ne]: OPTIONS.dealsOnWheelsStatus.DELETED,
          },
        },
      },
      scopes: {
        activeOrInactive: {
          where: {
            status: {
              [Op.in]: [
                OPTIONS.dealsOnWheelsStatus.ACTIVE,
                OPTIONS.dealsOnWheelsStatus.INACTIVE,
              ],
            },
          },
        },
      },
    }
  );

  return DealsOnWheels;
};
