const ServiceRequestRepository = require('../../../models/repositories/ServiceRequestRepository');
const { genRes, errorMessage, resCode } = require('../../../config/options');

exports.postServiceRequest = async (req, res) => {
  try {
    const userId = req.user.id;

    const { success, message, data } =
      await ServiceRequestRepository.createServiceRequest(userId, req.body);

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.getServiceRequestById = async (req, res) => {
  try {
    const id = req.params.id;

    const { success, message, data } =
      await ServiceRequestRepository.getServiceRequestById(id);

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.getServiceRequestListing = async (req, res) => {
  try {
    const userId = req.user.id;
    const { start = 0, limit = 10, status = null } = req.query;

    const { message, data } =
      await ServiceRequestRepository.getServiceRequestAndCount({
        userId,
        start: parseInt(start),
        limit: parseInt(limit),
        status,
      });

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.getServiceRequestListingByProvider = async (req, res) => {
  try {
    const providerId = req.user.id;
    const { start = 0, limit = 10, status = null } = req.query;

    const { message, data } =
      await ServiceRequestRepository.getServiceRequestAndCountByProvider({
        providerId,
        start: parseInt(start),
        limit: parseInt(limit),
        requestStatus: status,
      });

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.updateLeadStatus = async (req, res) => {
  try {
    const { id } = req.params;
    req.body.providerId = req.user.id;

    const { success, message, data } =
      await ServiceRequestRepository.checkAndPatchAcceptRejectServiceRequest(
        id,
        req.body
      );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.updateAppointment = async (req, res) => {
  try {
    const payload = {
      id: req.params.id,
      ...req.body,
      userRole: req.user.role,
      loggedUserId: req.user.id,
    };

    const { success, message, data } =
      await ServiceRequestRepository.checkAndPatchServiceRequestAppointment(
        payload
      );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};
