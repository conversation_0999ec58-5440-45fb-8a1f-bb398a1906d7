'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('ServiceRequestProvider', 'actionAt');
    await queryInterface.addColumn('ServiceRequestProvider', 'actionAt', {
      type: Sequelize.DATE,
      allowNull: true,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('ServiceRequestProvider', 'actionAt');
    await queryInterface.addColumn('ServiceRequestProvider', 'actionAt', {
      type: Sequelize.TIME,
      allowNull: true,
    });
  },
};
