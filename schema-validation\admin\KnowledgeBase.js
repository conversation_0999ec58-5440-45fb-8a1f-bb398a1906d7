const { knowledgeBaseStatus, usersRoles } = require('../../config/options');

const createAndUpdateKnowledgeBase = {
  title: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'title cannot be empty',
    },
    isString: {
      errorMessage: 'title must be a string',
    },
  },
  description: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'description cannot be empty',
    },
    isString: {
      errorMessage: 'description must be a string',
    },
  },
  thumbnail: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'thumbnail must be a string',
    },
  },
  status: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'status must be a string',
    },
    isIn: {
      options: [[knowledgeBaseStatus.DRAFT, knowledgeBaseStatus.ACTIVE]],
      errorMessage: `value must be ${(knowledgeBaseStatus.DRAFT, knowledgeBaseStatus.ACTIVE)}`,
    },
  },
  userType: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'userType cannot be empty',
    },
    isString: {
      errorMessage: 'status must be a string',
    },
    isIn: {
      options: [[usersRoles.CUSTOMER, usersRoles.PROVIDER]],
      errorMessage: `value must be ${(usersRoles.CUSTOMER, usersRoles.PROVIDER)}`,
    },
  },
};

module.exports = {
  createAndUpdateKnowledgeBase,
};
