const { genRes, errorMessage, resCode } = require('../../../config/options');
const { intentTool } = require('../../../tools/IntentTool');

exports.chat = async (req, res) => {
  try {
    const { message, sessionId } = req.body;
    const { message: replyMessage, data } = await intentTool.call({
      userMessage: message,
      sessionId,
    });
    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message: replyMessage, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};
