paths:
  /address:
    post:
      tags:
        - "Address"
      summary: "Set user zipcode with address"
      description: "Allows a logged-in user to set the address."
      operationId: "postAddress"
      security:
        - bearerAuth: [ ]
      requestBody:
        description: "Payload with new address"
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createOrUpdateAddress"
      produces:
        - "application/json"
      responses:
        "200":
          description: "Address added successfully"
        "400":
          description: "Invalid input"
        "500":
          description: "Internal Server Error"

    get:
      tags:
        - "Address"
      summary: "Get list of addresses"
      description: "Fetches a paginated list of user addresses. Requires authentication."
      operationId: "getAddressAndCount"
      security:
        - bearerAuth: []
      parameters:
        - name: start
          in: query
          required: false
          schema:
            type: integer
          description: "Offset for pagination (default: 0)"
        - name: limit
          in: query
          required: false
          schema:
            type: integer
          description: "Number of records to return (default: 10)"
      responses:
        "200":
          description: "List of addresses fetched successfully"
        "400":
          description: "Invalid request"
        "500":
          description: "Internal Server Error"

  /address/places:
    get:
      tags:
        - "Address"
      summary: "Get places from google places API"
      description: "Returns a list of places from Google Places Autocomplete API."
      operationId: "searchPlaces"
      security:
        - bearerAuth: [ ]
      parameters:
        - name: mode
          in: query
          required: true
          description: "Enter mode"
          schema:
            type: string
          enum: ["places"]
        - name: search
          in: query
          required: true
          description: "Add the value for address"
          schema:
            type: string
          minLength: 3
        - in: "query"
          name: "latitude"
          required: true
          schema:
            type: string
          description: "Enter latitude of location"
        - in: "query"
          name: "longitude"
          required: true
          schema:
            type: string
          description: "Enter longitude of location"   
      responses:
        "200":
          description: "List of places from google places API"
        "400":
          description: "Invalid input"
        "500":
          description: "Internal Server Error"

components:
  schemas:
    createOrUpdateAddress:
      type: object
      properties:
        zipcode:
          type: string
          example: "10001"
        latitude:
          type: number
          format: float
          example: 12.9716
        longitude:
          type: number
          format: float
          example: 77.5946
        category: 
          type: string
          example: "home"
          default: "home"
          enum: ["home", "work","other"]
        addressLine1:
          type: string
          example: "123 Main St"
        city:
          type: string
          example: "New York"
        state:
          type: string
          example: "NY"
      required:
        - addressLine1
        - state
        - zipcode