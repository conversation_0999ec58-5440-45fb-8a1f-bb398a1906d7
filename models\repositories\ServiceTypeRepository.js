const sequelize = require('sequelize');
const { ServiceType, ServiceCategory } = require('..');
const {
  successMessage,
  errorMessage,
  serviceStatus,
} = require('../../config/options');
const { Op, Sequelize } = sequelize;

const checkDuplicateServiceType = async (body, existingType = null) => {
  try {
    if (!body.name) return null;

    return await ServiceType.findOne({
      where: {
        name: body.name,
        ...(existingType?.id && {
          id: { [Op.ne]: existingType.id },
        }),
      },
      attributes: ['id', 'name', 'description', 'status'],
    });
  } catch (error) {
    throw new Error(error);
  }
};

const checkIfCategoryExists = async (categoryId) => {
  if (!categoryId) return true;

  const category = await ServiceCategory.findOne({
    where: { id: categoryId },
    attributes: ['id'],
  });

  return !!category;
};

const checkAndCreateServiceType = async (body) => {
  try {
    const categoryExists = await checkIfCategoryExists(body.categoryId);
    if (!categoryExists) {
      return {
        success: false,
        message: errorMessage.NO_USER('Service category'),
      };
    }

    const existingType = await checkDuplicateServiceType(body);

    if (!existingType) {
      const serviceType = await ServiceType.create(body);
      return {
        success: true,
        message: successMessage.SAVED_SUCCESS_MESSAGE('Service type'),
        data: serviceType,
      };
    }

    return {
      success: false,
      message: errorMessage.ALREADY_EXIST('Service type name'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndUpdateServiceType = async (serviceTypeId, body) => {
  try {
    const existingType = await ServiceType.findOne({
      where: { id: serviceTypeId },
    });

    if (!existingType) {
      return {
        success: false,
        message: errorMessage.NO_USER('Service type'),
      };
    }

    const duplicateType = await checkDuplicateServiceType(body, existingType);
    if (duplicateType) {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST('Service type name'),
      };
    }

    Object.assign(existingType, body);

    await existingType.save();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Service type'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

const getServiceTypeAndCount = async ({
  start = 0,
  limit = 10,
  search = null,
  categoryId = null,
}) => {
  try {
    const where = {
      ...(search && { name: { [Op.iLike]: `%${search}%` } }),
      ...(categoryId && { categoryId }),
    };

    const { count, rows } = await ServiceType.findAndCountAll({
      where,
      offset: Number(start),
      limit: Number(limit),
      order: [['createdAt', 'DESC']],
      attributes: [
        'id',
        'categoryId',
        'name',
        'imageUrl',
        'status',
        [
          Sequelize.literal(`(
            SELECT COUNT(*)
            FROM "SubService" AS ss
            WHERE ss."serviceTypeId" = "ServiceType"."id"
              AND ss."status" != 'deleted'
          )`),
          'subServiceCount',
        ],
        [
          Sequelize.literal(`(
            SELECT COUNT(*)
            FROM "ServiceOption" AS so
            INNER JOIN "SubService" AS ss ON so."subServiceId" = ss."id"
            WHERE ss."serviceTypeId" = "ServiceType"."id"
              AND ss."status" != 'deleted'
              AND so."status" != 'deleted'
          )`),
          'serviceOptionCount',
        ],
      ],
    });

    return {
      message: successMessage.DETAIL_MESSAGE('Service types'),
      data: {
        rows,
        pagination: {
          totalCount: count,
          start: Number(start),
          limit: Number(limit),
        },
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndUpdateStatus = async (serviceTypeId, isDeleted = false) => {
  try {
    const existingServiceType = await ServiceType.findOne({
      where: {
        id: serviceTypeId,
      },
      attributes: ['id', 'name', 'status'],
    });

    if (!existingServiceType) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Service type'),
      };
    }
    if (isDeleted) {
      existingServiceType.status = serviceStatus.DELETED;
    } else {
      existingServiceType.status =
        serviceStatus.ACTIVE === existingServiceType.status
          ? serviceStatus.BLOCKED
          : serviceStatus.ACTIVE;
    }
    await existingServiceType.save();

    return {
      success: true,
      message: successMessage.CHANGED_SUCCESS_MESSAGE('Service type status'),
      ...(!isDeleted && {
        data: existingServiceType,
      }),
    };
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndPatchServiceTypeStatus = async (id, isDelete) => {
  try {
    const { success, message } = await checkAndUpdateStatus(id, isDelete);
    return {
      success,
      message,
    };
  } catch (error) {
    throw new Error(error);
  }
};

module.exports = {
  checkAndCreateServiceType,
  checkAndUpdateServiceType,
  getServiceTypeAndCount,
  checkAndPatchServiceTypeStatus,
};
