const { Vehicle } = require('..');
const {
  successMessage,
  vehicleStatus,
  vehicleOptions,
} = require('../../config/options');
const { decodeVin } = require('../helpers/VehicleHelper');
const { Op } = require('sequelize');

const createVehicle = async (userId, body) => {
  try {
    const vehicle = await Vehicle.create({
      ...body,
      userId,
    });

    return {
      success: true,
      message: successMessage.SAVED_SUCCESS_MESSAGE('Vehicle'),
      data: vehicle,
    };
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndUpdateVehicle = async (userId, vehicleId, body) => {
  try {
    const existingVehicle = await Vehicle.findOne({
      where: {
        id: vehicleId,
        userId,
      },
    });

    if (!existingVehicle) {
      return {
        success: false,
        message: errorMessage.NOT_FOUND('Vehicle'),
      };
    }

    Object.keys(body).forEach((key) => {
      if (body[key] !== undefined) {
        existingVehicle[key] = body[key];
      }
    });

    await existingVehicle.save();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Vehicle'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

const getVehicleAndCount = async ({
  userId,
  start = 0,
  limit = 10,
  search,
}) => {
  try {
    const where = {
      userId,
    };

    if (search && !['all', 'null', 'undefined'].includes(search)) {
      where[Op.or] = [
        { vehicleType: { [Op.iLike]: `%${search}%` } },
        { industry: { [Op.iLike]: `%${search}%` } },
      ];
    }

    const { count, rows } = await Vehicle.findAndCountAll({
      where,
      offset: Number(start),
      limit: Number(limit),
      order: [['createdAt', 'DESC']],
      attributes: [
        'id',
        'vehicleType',
        'year',
        'industry',
        'model',
        'vehicleTrim',
        'engine',
        'driveTrain',
        'milage',
        'vin',
        'plateNumber',
        'state',
        'createdAt',
        'updatedAt',
      ],
    });

    return {
      message: successMessage.DETAIL_MESSAGE('Vehicle'),
      data: {
        rows,
        pagination: {
          totalCount: count,
          start: Number(start),
          limit: Number(limit),
        },
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndGetVehicle = async (id, userId) => {
  const existingVehicle = await Vehicle.findOne({
    where: {
      id,
      userId,
    },
    attributes: [
      'id',
      'vehicleType',
      'year',
      'vehicleType',
      'industry',
      'model',
      'vehicleTrim',
      'engine',
      'driveTrain',
      'milage',
      'vin',
      'plateNumber',
      'state',
      'createdAt',
      'updatedAt',
    ],
  });

  if (!existingVehicle) {
    return {
      success: false,
      message: errorMessage.DOES_NOT_EXIST('Vehicle'),
    };
  }

  return {
    success: true,
    data: existingVehicle,
    message: successMessage.DETAIL_MESSAGE('Vehicle'),
  };
};

const checkAndDelete = async (vehicleId, userId) => {
  try {
    const existingVehicle = await Vehicle.findOne({
      where: {
        id: vehicleId,
        userId,
      },
    });

    if (!existingVehicle) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Vehicle'),
      };
    }

    existingVehicle.status = vehicleStatus.DELETED;
    await existingVehicle.save();

    return {
      success: true,
      message: successMessage.CHANGED_SUCCESS_MESSAGE('Vehicle status'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

const getVehicleDropdownOptions = ({ type, year, make, model, trim }) => {
  let data = {};

  if (!type) {
    data = { types: vehicleOptions.types };
  } else if (!year) {
    data = { years: vehicleOptions.years };
  } else if (!make) {
    const key = `${type}-${year}`;
    data = { makes: vehicleOptions.makes[key] || [] };
  } else if (!model) {
    data = { models: vehicleOptions.models[make] || [] };
  } else if (!trim) {
    data = { trims: vehicleOptions.trims[model] || [] };
  } else {
    data = {
      engines: vehicleOptions.engines[trim] || [],
      drivetrains: vehicleOptions.drivetrains,
    };
  }

  return {
    message: successMessage.DETAIL_MESSAGE('Vehicle options'),
    data,
  };
};

const createVehicleFromVin = async (userId, body) => {
  try {
    const { vin } = body;

    const vinResponse = await decodeVin(vin);

    if (!vinResponse.success || !vinResponse.data) {
      return {
        success: false,
        message: vinResponse.message,
        data: null,
      };
    }

    const vehicleDetails = vinResponse.data;

    const vehicle = await Vehicle.create({
      userId,
      vin: vehicleDetails.vin,
      year: vehicleDetails.year,
      driveTrain: vehicleDetails.driveTrain,
      industry: vehicleDetails.make,
      vehicleTrim: vehicleDetails.trim,
      model: vehicleDetails.model,
      engine: vehicleDetails.engine,
      vehicleType: vehicleDetails.vehicleType,
    });

    return {
      success: true,
      message: successMessage.SAVED_SUCCESS_MESSAGE('Vehicle'),
      data: vehicle,
    };
  } catch (error) {
    throw new Error(error);
  }
};

module.exports = {
  createVehicle,
  checkAndUpdateVehicle,
  getVehicleAndCount,
  checkAndGetVehicle,
  checkAndDelete,
  getVehicleDropdownOptions,
  createVehicleFromVin,
};
