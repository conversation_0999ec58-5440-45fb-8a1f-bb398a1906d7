const {
  knowledgeBaseStatus,
  usersRoles,
  generateCloudFrontUrl,
} = require('../config/options');
const { Op } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  const KnowledgeBase = sequelize.define(
    'KnowledgeBase',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      articleCode: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
      },
      title: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      thumbnail: {
        allowNull: true,
        type: DataTypes.TEXT,
        get() {
          return generateCloudFrontUrl(this.getDataValue('thumbnail'));
        },
        set(file) {
          if (file) {
            this.setDataValue(
              'thumbnail',
              `uploads/${file.split('uploads/')[1]}`
            );
          }
        },
      },
      userType: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: usersRoles.CUSTOMER,
      },
      status: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: knowledgeBaseStatus.ACTIVE,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
    },
    {
      tableName: 'KnowledgeBase',
      timestamps: true,
      defaultScope: {
        where: {
          [Op.and]: [
            { status: knowledgeBaseStatus.ACTIVE },
            { status: { [Op.ne]: knowledgeBaseStatus.DELETED } },
          ],
        },
      },

      scopes: {
        withDeleted: {
          // No conditions here to include all records including deleted
        },
        onlyDeleted: {
          where: {
            status: knowledgeBaseStatus.DELETED,
          },
        },
      },
    }
  );

  KnowledgeBase.associate = function (models) {
    KnowledgeBase.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'user',
    });
  };

  return KnowledgeBase;
};
