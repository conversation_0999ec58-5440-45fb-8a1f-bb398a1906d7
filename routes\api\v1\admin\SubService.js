const { Router } = require('express');

const router = Router();
const { checkSchema } = require('express-validator');

const {
  postSubService,
  putSubService,
  patchChangeStatusSubService,
  deleteSubService,
  getSubServiceList,
} = require('../../../../controllers/api/v1/admin/SubService');

const {
  requestValidator,
} = require('../../../../models/helpers/ErrorHandleHelper');
const {
  validateCreateSubService,
  validateUpdateSubService,
} = require('../../../../schema-validation/admin/SubService');

router.post(
  '/',
  checkSchema(validateCreateSubService),
  requestValidator,
  postSubService
);

router.put(
  '/:id',
  checkSchema(validateUpdateSubService),
  requestValidator,
  putSubService
);

router.get('/', getSubServiceList);

router.patch('/:id', patchChangeStatusSubService);

router.delete('/:id', deleteSubService);

module.exports = router;
