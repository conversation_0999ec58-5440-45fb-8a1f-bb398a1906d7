'use strict';

const { serviceStatus } = require('../config/options');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('Service', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      name: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      imageUrl: {
        allowNull: false,
        type: Sequelize.TEXT,
      },
      bannerUrl: {
        allowNull: false,
        type: Sequelize.TEXT,
      },
      status: {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: serviceStatus.ACTIVE,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    await queryInterface.removeColumn('ServiceCategory', 'imageUrl');
    await queryInterface.addColumn('ServiceCategory', 'serviceId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Service',
        key: 'id',
      },
      onDelete: 'CASCADE',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('Service');

    await queryInterface.addColumn('ServiceCategory', 'imageUrl', {
      type: Sequelize.TEXT,
    });
    await queryInterface.removeColumn('ServiceCategory', 'serviceId');
  },
};
