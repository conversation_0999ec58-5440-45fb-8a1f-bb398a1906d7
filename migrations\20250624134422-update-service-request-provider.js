'use strict';

const {
  serviceRequestProviderStatus,
  serviceRequestStatus,
} = require('../config/options');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn(
      'ServiceRequestProvider',
      'requestStatus',
      {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: serviceRequestProviderStatus.PENDING,
      }
    );
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn(
      'ServiceRequestProvider',
      'requestStatus',
      {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: serviceRequestStatus.PENDING,
      }
    );
  },
};
