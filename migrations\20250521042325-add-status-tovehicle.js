'use strict';

const { vehicleStatus } = require('../config/options');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('Vehicle', 'status', {
      type: Sequelize.STRING,
      allowNull: false,
      defaultValue: vehicleStatus.ACTIVE,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('Vehicle', 'status');
  },
};
