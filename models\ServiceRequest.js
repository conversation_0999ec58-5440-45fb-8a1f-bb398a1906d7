const {
  propertyLocation,
  serviceDuration,
  serviceRequestStatus,
} = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const ServiceRequest = sequelize.define(
    'ServiceRequest',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      scheduledAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      status: {
        type: DataTypes.STRING,
        allowNull: true,
        defaultValue: serviceRequestStatus.PENDING,
      },
      propertyLocation: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: propertyLocation.GARAGE,
      },
      otherLocation: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      specialInstruction: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      serviceDuration: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: serviceDuration.URGENT,
      },
      rescheduleReason: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      cancellationReason: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      serviceStartedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      serviceCompletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      note: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
    },
    {
      tableName: 'ServiceRequest',
      timestamps: true,
    }
  );

  ServiceRequest.associate = function (models) {
    ServiceRequest.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE',
    });

    ServiceRequest.belongsTo(models.User, {
      foreignKey: 'cancelledById',
      as: 'cancelledBy',
      onDelete: 'CASCADE',
    });

    ServiceRequest.belongsTo(models.User, {
      foreignKey: 'rescheduledById',
      as: 'rescheduledBy',
      onDelete: 'CASCADE',
    });

    ServiceRequest.belongsTo(models.Vehicle, {
      foreignKey: 'vehicleId',
      as: 'vehicle',
      onDelete: 'CASCADE',
    });

    ServiceRequest.belongsTo(models.Address, {
      foreignKey: 'addressId',
      as: 'address',
      onDelete: 'CASCADE',
    });

    ServiceRequest.hasMany(models.ServiceRequestOption, {
      foreignKey: 'requestId',
      as: 'serviceOptions',
      onDelete: 'CASCADE',
    });

    //Request accepted by provider
    ServiceRequest.belongsTo(models.User, {
      foreignKey: 'providerId',
      as: 'provider',
      onDelete: 'CASCADE',
    });

    //Request sent to providers
    ServiceRequest.hasMany(models.ServiceRequestProvider, {
      foreignKey: 'requestId',
      as: 'serviceProviders',
      onDelete: 'CASCADE',
    });
  };

  return ServiceRequest;
};
