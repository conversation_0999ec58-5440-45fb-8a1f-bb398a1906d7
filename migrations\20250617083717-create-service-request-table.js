'use strict';

const {
  locationType,
  serviceDuration,
  serviceStatus,
} = require('../config/options');

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('ServiceRequest', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'User',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      vehicleId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Vehicle',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      addressId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Address',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      serviceOptionId: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'ServiceOption',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      note: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      scheduledAt: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      status: {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: serviceStatus.ACTIVE,
      },
      locationType: {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: locationType.GARAGE,
      },
      otherLocation: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      specialInstruction: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      serviceDuration: {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: serviceDuration.URGENT,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('ServiceRequest');
  },
};
