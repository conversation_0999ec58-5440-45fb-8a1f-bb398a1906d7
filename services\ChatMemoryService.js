const { ConversationSummaryBufferMemory } = require('langchain/memory');
const { ChatGoogleGenerativeAI } = require('@langchain/google-genai');
const options = require('../config/options');

const memoryMap = new Map();

function getOrCreateMemory(sessionId) {
  let memory = memoryMap.get(sessionId);
  if (!memory) {
    memory = new ConversationSummaryBufferMemory({
      llm: new ChatGoogleGenerativeAI({
        model: process.env.GEMINI_MODEL || 'gemini-2.0-flash',
        apiKey: process.env.GOOGLE_API_KEY,
        temperature: 0.1,
      }),
      memoryKey: 'history',
      returnMessages: true,
      maxTokenLimit: 1000,
    });
    memoryMap.set(sessionId, memory);
  }
  return memory;
}

exports.getHistory = async (sessionId) => {
  const memory = getOrCreateMemory(sessionId);
  const { history } = await memory.loadMemoryVariables({});
  return history || [];
};

exports.appendToHistory = async (sessionId, role, content) => {
  const memory = getOrCreateMemory(sessionId);
  // Lang<PERSON>hain expects { role: 'user' | 'assistant', content }
  // await memory.saveContext(
  //   role === options.chatRole.USER ? { input: content } : { output: content },
  //   {}
  // );
  if (role === options.chatRole.USER) {
    await memory.saveContext({ input: content }, { output: '' }); // user input, no assistant response yet
  } else {
    await memory.saveContext({ input: '' }, { output: content }); // assistant response
  }
};

exports.clearHistory = (sessionId) => {
  memoryMap.delete(sessionId);
};

exports.getFormattedHistory = async (sessionId) => {
  const memory = getOrCreateMemory(sessionId);
  const { history } = await memory.loadMemoryVariables({});
  // history is an array of messages: { role, content }
  return (history || [])
    .map(
      (msg) =>
        `${msg.role === options.chatRole.USER ? 'User' : options.chatRole.ASSISTANT}: ${msg.content}`
    )
    .join('\n');
};
