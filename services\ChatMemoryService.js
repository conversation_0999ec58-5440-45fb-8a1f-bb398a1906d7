const options = require('../config/options');

// Simple in-memory storage for conversation history
const conversationHistory = new Map();

function getOrCreateHistory(sessionId) {
  if (!conversationHistory.has(sessionId)) {
    conversationHistory.set(sessionId, []);
  }
  return conversationHistory.get(sessionId);
}

exports.getHistory = async (sessionId) => {
  return getOrCreateHistory(sessionId);
};

exports.appendToHistory = async (sessionId, role, content) => {
  const history = getOrCreateHistory(sessionId);
  history.push({
    role: role,
    content: content,
    timestamp: new Date().toISOString(),
  });
};

exports.clearHistory = (sessionId) => {
  conversationHistory.delete(sessionId);
};

exports.getFormattedHistory = async (sessionId) => {
  const history = getOrCreateHistory(sessionId);
  return history
    .map((msg) => {
      const label = msg.role === options.chatRole.USER ? 'User' : 'Revi';
      return `${label}: ${msg.content}`;
    })
    .join('\n');
};
