const { serviceInterval } = require('../../config/options');

const createAndUpdateServiceOption = {
  options: {
    in: ['body'],
    isArray: {
      errorMessage: 'Options must be an array',
    },
    notEmpty: {
      errorMessage: 'Options cannot be empty',
    },
  },
  'options.*.name': {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'name cannot be empty',
    },
    isString: {
      errorMessage: 'name must be a string',
    },
  },
  'options.*.serviceIntervalType': {
    optional: true,
    isIn: {
      options: [[serviceInterval.DAYS, serviceInterval.MILES]],
      errorMessage: `Service interval type must be ${(serviceInterval.DAYS, serviceInterval.MILES)}`,
    },
  },
  'options.*.serviceIntervalValue': {
    optional: true,
    isString: {
      errorMessage: 'Service interval value must be a string',
    },
  },
  subServiceId: {
    in: ['query'],
    trim: true,
    notEmpty: {
      errorMessage: 'subServiceId cannot be empty',
    },
    isUUID: {
      errorMessage: 'subServiceId must be a valid UUID',
    },
  },
};

const updateServiceOption = {
  id: {
    in: ['params'],
    notEmpty: true,
    errorMessage: 'id is required',
    isUUID: {
      errorMessage: 'id must be a valid UUID',
    },
  },
  subServiceId: {
    in: ['body'],
    notEmpty: true,
    trim: true,
    errorMessage: 'subServiceId is required',
    isUUID: {
      errorMessage: 'subServiceId must be a valid UUID',
    },
  },
  name: {
    in: ['body'],
    notEmpty: true,
    trim: true,
    errorMessage: 'name is required',
    isString: {
      errorMessage: 'name must be a string',
    },
  },
  serviceIntervalType: {
    in: ['body'],
    optional: true,
    isIn: {
      options: [[serviceInterval.DAYS, serviceInterval.MILES]],
      errorMessage: `Service interval type must be ${(serviceInterval.DAYS, serviceInterval.MILES)}`,
    },
  },
  serviceIntervalValue: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Service interval value must be a string',
    },
  },
};

module.exports = {
  createAndUpdateServiceOption,
  updateServiceOption,
};
