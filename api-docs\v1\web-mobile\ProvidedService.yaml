paths:
  /provided-service:
    post:
      tags:
        - "ProvidedService"
      summary: "Create Provided Service"
      description: "Allows a provider to create a provided service with types and optional cancellation policy"
      operationId: "createProvidedService"
      security:
        - bearerAuth: []
      requestBody:
        description: "Payload to create a provided service"
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/providedServicePayload"
      responses:
        "201":
          description: "Provided service created successfully"
        "400":
          description: "Invalid input"
        "500":
          description: "Internal Server Error"

    get:
      tags:
        - "ProvidedService"
      summary: "Get Provided Service details by user ID"
      description: "Fetch provided service details using its user ID."
      operationId: "getProvidedService"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Provided Service details retrieved successfully"
        "404":
          description: "Provided service not found"
        "500":
          description: "Internal Server Error"

    put:
      tags:
        - "ProvidedService"
      summary: "Update Provided Service"
      description: "Update an existing provided service by user"
      operationId: "updateProvidedService"
      security:
        - bearerAuth: []
      requestBody:
        description: "Payload to update a provided service"
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/providedServicePayload"
      responses:
        "200":
          description: "Provided service updated successfully"
        "400":
          description: "Invalid input"
        "404":
          description: "Provided service not found"
        "500":
          description: "Internal Server Error"

components:
  schemas:
    providedServicePayload:
      type: object
      required:
        - serviceTypes
      properties:
        note:
          type: string
          example: "Other service details if any"
        serviceTypes:
          type: array
          description: "Array of service types with pricing"
          items:
            type: object
            required:
              - serviceTypeId
            properties:
              serviceTypeId:
                type: string
                example: "f7a91c94-47f2-4147-aae4-5113e238ae0c"
              minPrice:
                type: string
                example: "50.00"
              maxPrice:
                type: string
                example: "150.00"
        cancellationPolicy:
          type: object
          description: "Optional cancellation policy for this service"
          properties:
            startTime:
              type: string
              format: time
              example: "00:00:00"
            endTime:
              type: string
              format: time
              example: "23:59:59"
            cancellationFee:
              type: string
              example: "20.00"

        availability:
          type: array
          description: "Weekly availability with one or more time slots"
          items:
            type: object
            properties:
              dayOfWeek:
                type: string
                example: "monday"
                enum:
                  - monday
                  - tuesday
                  - wednesday
                  - thursday
                  - friday
                  - saturday
                  - sunday
              slots:
                type: array
                description: "Time slots for the given day"
                items:
                  type: object
                  required:
                    - startTime
                    - endTime
                  properties:
                    startTime:
                      type: string
                      format: time
                      example: "10:00:00"
                    endTime:
                      type: string
                      format: time
                      example: "13:00:00"

        address:
          type: object
          description: "Service location details"
          required:
            - zipcode
            - latitude
            - longitude
            - radius
          properties:
            zipcode:
              type: string
              example: "560001"
            city:
              type: string
              example: "newtown"
            state:
              type: string
              example: "burlin"
            addressLine1:
              type: string
              example: "addressLine1"
            latitude:
              type: number
              format: float
              example: 12.9716
            longitude:
              type: number
              format: float
              example: 77.5946
            radius:
              type: number
              format: float
              description: "Service coverage radius in kilometers"
              example: 5.0
