'use strict';

const { usersRoles } = require('../config/options');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('KnowledgeBase', 'userType', {
      type: Sequelize.ENUM,
      values: [usersRoles.CUSTOMER, usersRoles.PROVIDER],
      allowNull: false,
      defaultValue: usersRoles.CUSTOMER,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('KnowledgeBase', 'userType');
  },
};
