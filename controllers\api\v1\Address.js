const AddressRepository = require('../../../models/repositories/AddressRepository');
const { genRes, errorMessage, resCode } = require('../../../config/options');

exports.postAddress = async (req, res) => {
  try {
    const userId = req.user.id;

    const { success, message, data } = await AddressRepository.createAddress(
      userId,
      req.body
    );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.searchPlaces = async (req, res) => {
  try {
    const { search, mode, latitude = null, longitude = null } = req.query;

    const { success, message, data } = await AddressRepository.searchPlaces({
      search,
      mode,
      latitude,
      longitude,
    });

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.getAddressListing = async (req, res) => {
  try {
    const userId = req.user.id;
    const { start = 0, limit = 10 } = req.query;

    const { message, data } = await AddressRepository.getAddressAndCount({
      userId,
      start: parseInt(start),
      limit: parseInt(limit),
    });

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};
