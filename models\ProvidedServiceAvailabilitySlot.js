module.exports = (sequelize, DataTypes) => {
  const ProvidedServiceAvailabilitySlot = sequelize.define(
    'ProvidedServiceAvailabilitySlot',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      startTime: {
        type: DataTypes.TIME,
        allowNull: false,
        defaultValue: '00:00:00',
      },
      endTime: {
        type: DataTypes.TIME,
        allowNull: false,
        defaultValue: '23:59:59',
      },
    },
    {
      tableName: 'ProvidedServiceAvailabilitySlot',
      timestamps: true,
    }
  );

  ProvidedServiceAvailabilitySlot.associate = function (models) {
    ProvidedServiceAvailabilitySlot.belongsTo(
      models.ProvidedServiceAvailability,
      {
        foreignKey: 'availabilityId',
        as: 'availability',
        onDelete: 'CASCADE',
      }
    );
  };

  return ProvidedServiceAvailabilitySlot;
};
