paths:
  /chatbot:
    post:
      tags:
        - "Chatbot"
      summary: "Chat with LLM Chatbot"
      description: "Sends a message to the LLM-powered chatbot and receives a reply."
      operationId: "chatWithBot"
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                message:
                  type: string
                  description: "The message to send to the chatbot."
                sessionId:
                  type: string
                  description: "(Optional) Session ID for chat context. If not provided, a default session is used."
              required:
                - message
      responses:
        "200":
          description: "Chatbot reply returned successfully."
          content:
            application/json:
              schema:
                type: object
                properties:
                  reply:
                    type: string
                    description: "The chatbot's reply."
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error" 