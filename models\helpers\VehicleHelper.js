const axios = require('axios');
const { errorMessage, successMessage } = require('../../config/options');

const apiService = axios.create({
  baseURL: 'https://carapi.app/api',
  headers: {
    Accept: 'application/json',
    'Content-Type': 'application/json;charset=UTF-8',
  },
});

async function decodeVin(vin) {
  try {
    const res = await apiService.get(`/vin/${vin}?verbose=yes&all_trims=yes`);
    const data = res.data;

    if (!data || !data.make || !data.model) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('make & model'),
        data: null,
      };
    }

    const vehicleData = {
      year: data.year,
      make: data.make,
      model: data.model,
      trim: data.trim,
      driveTrain: data.specs?.drive_type,
      engine: data.specs?.other_engine_info,
      vehicleType: data.specs?.vehicle_type,
      vin,
    };

    return {
      success: true,
      message: successMessage.SAVED_SUCCESS_MESSAGE('Vehicle Details'),
      data: vehicleData,
    };
  } catch (err) {
    return {
      success: false,
      message: errorMessage.NO_USER('vin'),
      data: null,
    };
  }
}

module.exports = {
  decodeVin,
};
