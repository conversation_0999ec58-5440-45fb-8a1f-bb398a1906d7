const { usersRoles } = require('../config/options');

exports.sendOtp = {
  type: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Type cannot be empty',
    isIn: {
      options: [['email', 'mobileNumber']],
      errorMessage: `Type value must be email or mobileNumber`,
    },
    isString: {
      errorMessage: 'Type must be string',
    },
  },
  countryCode: {
    in: ['body'],
    trim: true,
    notEmpty: {
      if: (value, { req, location, path }) => req.body.type !== 'email',
      else: (value) => false,
    },
    errorMessage: 'Country code cannot be empty',
    isString: {
      errorMessage: 'Country code must be string',
    },
    customSanitizer: {
      options: (value, { req, location, path }) => {
        return value.charAt(0) === '+'
          ? value.substring(1, value.length)
          : value;
      },
    },
  },
  mobileNumber: {
    in: ['body'],
    trim: true,
    notEmpty: {
      if: (value, { req, location, path }) => req.body.type !== 'email',
      else: (value) => false,
    },
    errorMessage: 'Mobile number cannot be empty',
    isString: {
      errorMessage: 'Mobile number must be string',
    },
  },
  email: {
    in: ['body'],
    trim: true,
    notEmpty: {
      if: (value, { req, location, path }) => req.body.type === 'email',
      else: (value) => false,
    },
    errorMessage: 'Email cannot be empty',
    isString: {
      errorMessage: 'Email must be string',
    },
    isEmail: {
      bail: true,
      errorMessage: 'Enter a valid Email',
    },
  },
  role: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Role cannot be empty',
    },
    isIn: {
      options: [[usersRoles.CUSTOMER, usersRoles.PROVIDER]],
      errorMessage: `value must be ${(usersRoles.CUSTOMER, usersRoles.PROVIDER)}`,
    },
  },
};

exports.verifyOtp = {
  type: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Type cannot be empty',
    isIn: {
      options: [['email', 'mobileNumber']],
      errorMessage: `Type value must be email or mobileNumber`,
    },
    isString: {
      errorMessage: 'Type must be string',
    },
  },
  tempOtp: {
    in: ['body'],
    notEmpty: true,
    errorMessage: 'Temp OTP cannot be empty',
    isInt: {
      errorMessage: 'Temp OTP must be integer',
    },
  },
  countryCode: {
    in: ['body'],
    trim: true,
    notEmpty: {
      if: (value, { req, location, path }) => req.body.type !== 'email',
      else: (value) => false,
    },
    errorMessage: 'Country code cannot be empty',
    isString: {
      errorMessage: 'Country code must be string',
    },
    customSanitizer: {
      options: (value, { req, location, path }) => {
        return value.charAt(0) === '+'
          ? value.substring(1, value.length)
          : value;
      },
    },
  },
  mobileNumber: {
    in: ['body'],
    trim: true,
    notEmpty: {
      if: (value, { req, location, path }) => req.body.type !== 'email',
      else: (value) => false,
    },
    errorMessage: 'Mobile number cannot be empty',
    isString: {
      errorMessage: 'Mobile number must be string',
    },
  },
  email: {
    in: ['body'],
    trim: true,
    notEmpty: {
      if: (value, { req, location, path }) => req.body.type === 'email',
      else: (value) => false,
    },
    errorMessage: 'Email cannot be empty',
    isString: {
      errorMessage: 'Email must be string',
    },
    isEmail: {
      bail: true,
      errorMessage: 'Enter a valid Email',
    },
  },
  role: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Role cannot be empty',
    },
    isIn: {
      options: [[usersRoles.CUSTOMER, usersRoles.PROVIDER]],
      errorMessage: `value must be ${(usersRoles.CUSTOMER, usersRoles.PROVIDER)}`,
    },
  },
};

exports.updateInfo = {
  firstName: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'First name must be string',
    },
  },
  lastName: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'Last name must be string',
    },
  },
  profilePicture: {
    in: ['body'],
    trim: true,
    notEmpty: {
      if: (value, { req, location, path }) => !!req.body.profilePicture,
      else: (value) => false,
    },
  },
  email: {
    in: ['body'],
    optional: true,
    trim: true,
    isEmail: {
      errorMessage: 'Invalid email address',
    },
  },
  countryCode: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'country Code must be string',
    },
  },
  mobileNumber: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'Mobile number must be string',
    },
    isLength: {
      options: { min: 10, max: 15 },
      errorMessage: 'Mobile number must be between 10 to 15 digits',
    },
  },
};

exports.signUp = {
  countryCode: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Country code cannot be empty',
    isString: {
      errorMessage: 'Country code must be string',
    },
    customSanitizer: {
      options: (value, { req, location, path }) => {
        return value.charAt(0) === '+'
          ? value.substring(1, value.length)
          : value;
      },
    },
  },
  mobileNumber: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Mobile number cannot be empty',
    isString: {
      errorMessage: 'Mobile number must be string',
    },
  },
  firstName: {
    in: ['body'],
    optional: { options: { nullable: true } },
    trim: true,
    isString: {
      errorMessage: 'First name must be string',
    },
  },
  lastName: {
    in: ['body'],
    optional: { options: { nullable: true } },
    trim: true,
    isString: {
      errorMessage: 'Last name must be string',
    },
  },
  email: {
    in: ['body'],
    optional: { options: { nullable: true } },
    trim: true,
    isString: {
      errorMessage: 'email must be string',
    },
  },
  role: {
    in: ['body'],
    optional: true,
    isIn: {
      options: [[usersRoles.CUSTOMER, usersRoles.PROVIDER]],
      errorMessage: `Role must be ${(usersRoles.CUSTOMER, usersRoles.PROVIDER)}`,
    },
  },
};

exports.setEmail = {
  email: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Email cannot be empty',
    },
    isEmail: {
      errorMessage: 'Must be a valid email address',
    },
    normalizeEmail: true,
  },
};

exports.sso = {
  ssoProvider: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'sso provider cannot be empty',
    },
    isIn: {
      options: [['google', 'apple', 'facebook']],
      errorMessage: `SSO Provider value must be 'google', 'apple', 'facebook'`,
    },
  },
  googleId: {
    in: ['body'],
    trim: true,
    custom: {
      options: (value, { req }) => {
        if (req.body.ssoProvider === 'google') {
          if (!value) {
            throw new Error(
              'Google id cannot be empty when ssoProvider is google'
            );
          }
        } else if (value) {
          throw new Error(
            'Google id must be empty unless ssoProvider is google'
          );
        }
        return true;
      },
    },
  },
  appleId: {
    in: ['body'],
    trim: true,
    custom: {
      options: (value, { req }) => {
        if (req.body.ssoProvider === 'apple') {
          if (!value) {
            throw new Error(
              'Apple id cannot be empty when ssoProvider is apple'
            );
          }
        } else if (value) {
          throw new Error('Apple id must be empty unless ssoProvider is apple');
        }
        return true;
      },
    },
  },
  facebookId: {
    in: ['body'],
    trim: true,
    custom: {
      options: (value, { req }) => {
        if (req.body.ssoProvider === 'facebook') {
          if (!value) {
            throw new Error(
              'Facebook id cannot be empty when ssoProvider is facebook'
            );
          }
        } else if (value) {
          throw new Error(
            'Facebook id must be empty unless ssoProvider is facebook'
          );
        }
        return true;
      },
    },
  },
  idToken: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Id token cannot be empty',
    },
  },
  firstName: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'first name cannot be empty',
    },
  },
  lastName: {
    in: ['body'],
    trim: true,
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'last name must be string',
    },
  },
  role: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Role cannot be empty',
    },
    isIn: {
      options: [[usersRoles.CUSTOMER, usersRoles.PROVIDER]],
      errorMessage: `SSO Provider value must be ${(usersRoles.CUSTOMER, usersRoles.PROVIDER)}`,
    },
  },
  email: {
    in: ['body'],
    trim: true,
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'Type must be string',
    },
  },
};
