'use strict';

const { serviceStatus } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      await queryInterface.bulkInsert('Service', [
        {
          name: 'Book Emergency Services',
          status: serviceStatus.ACTIVE,
          imageUrl: 'uploads/17519645887289f6h05Appointments.png',
          bannerUrl: 'uploads/1751964822455arx828tirechange21.png',
        },
        {
          name: 'Schedule Repair & Maintenance',
          status: serviceStatus.ACTIVE,
          imageUrl: 'uploads/17519646367742t1cs9Appointments1.png',
          bannerUrl: 'uploads/1751964880520pwkjqtirechange22.png',
        },
        {
          name: 'Convenience & Support Services',
          status: serviceStatus.ACTIVE,
          imageUrl: 'uploads/175196469330822ca9jAppointments2.png',
          bannerUrl: 'uploads/1751964982523m2fpkrtirechange24.png',
        },
        {
          name: 'Virtual Assistive Services',
          status: serviceStatus.ACTIVE,
          imageUrl: 'uploads/1751964743733e2o2tfAppointments3.png',
          bannerUrl: 'uploads/17519649320391t0ststirechange23.png',
        },
      ]);
    } catch (error) {
      console.error('Error inserting super admin:', error);
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('Service', null, {});
  },
};
