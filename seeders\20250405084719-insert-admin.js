'use strict';

const { usersRoles, userStatus } = require('../config/options');
const { generatePassword } = require('../models/helpers/UtilHelper');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      const email = '<EMAIL>';
      const password = 'revilo@2025';

      const passwordHash = await generatePassword(password);
      await queryInterface.bulkInsert('User', [
        {
          email,
          password: passwordHash,
          role: usersRoles.SUPER_ADMIN,
          firstName: 'Super',
          lastName: 'Admin',
          countryCode: '91',
          mobileNumber: '9999999999',
          status: userStatus.ACTIVE,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ]);
    } catch (error) {
      console.error('Error inserting super admin:', error);
    }
  },

  async down(queryInterface, Sequelize) {
    const email = '<EMAIL>';
    await queryInterface.bulkDelete('User', {
      email,
      role: usersRoles.SUPER_ADMIN,
    });
  },
};
