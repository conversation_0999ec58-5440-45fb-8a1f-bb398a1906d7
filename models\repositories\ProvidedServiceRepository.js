const {
  ProvidedService,
  ProvidedServiceType,
  ProvidedServiceCancellationPolicy,
  ProvidedServiceAvailability,
  ProvidedServiceAvailabilitySlot,
  Address,
  ServiceType,
} = require('..');
const { successMessage } = require('../../config/options');
const AddressRepository = require('./AddressRepository');

const createProviderService = async (userId, body) => {
  const transaction = await ProvidedService.sequelize.transaction();

  try {
    const {
      note,
      serviceTypes = [],
      cancellationPolicy = {},
      availability = [],
      address = {},
    } = body;

    const providedService = await ProvidedService.create(
      {
        userId,
        note,
      },
      { transaction }
    );

    if (serviceTypes.length > 0) {
      const serviceTypePayload = serviceTypes.map((item) => ({
        providedServiceId: providedService.id,
        serviceTypeId: item.serviceTypeId,
        minPrice: item.minPrice,
        maxPrice: item.maxPrice,
      }));

      await ProvidedServiceType.bulkCreate(serviceTypePayload, { transaction });
    }

    if (
      cancellationPolicy &&
      (cancellationPolicy.startTime ||
        cancellationPolicy.endTime ||
        cancellationPolicy.cancellationFee)
    ) {
      await ProvidedServiceCancellationPolicy.create(
        {
          userId,
          startTime: cancellationPolicy.startTime,
          endTime: cancellationPolicy.endTime,
          cancellationFee: cancellationPolicy.cancellationFee,
        },
        { transaction }
      );
    }

    if (availability.length > 0) {
      for (const item of availability) {
        const availabilityRecord = await ProvidedServiceAvailability.create(
          {
            userId,
            dayOfWeek: item.dayOfWeek,
          },
          { transaction }
        );

        const slotPayload = (item.slots || []).map((slot) => ({
          availabilityId: availabilityRecord.id,
          startTime: slot.startTime,
          endTime: slot.endTime,
        }));

        if (slotPayload.length > 0) {
          await ProvidedServiceAvailabilitySlot.bulkCreate(slotPayload, {
            transaction,
          });
        }
      }
    }

    const { zipcode, latitude, longitude, radius, addressLine1, city, state } =
      address;

    if (zipcode && latitude && longitude) {
      await AddressRepository.createAddress(userId, {
        zipcode,
        latitude,
        longitude,
        radius,
        addressLine1,
        city,
        state,
      });
    }

    await transaction.commit();

    return {
      success: true,
      message: successMessage.SAVED_SUCCESS_MESSAGE('Provided Service'),
    };
  } catch (error) {
    await transaction.rollback();
    throw new Error(error);
  }
};

const checkAndGetProviderService = async (userId) => {
  const providedService = await ProvidedService.findOne({
    where: { userId },
    attributes: ['id', 'note'],
    include: [
      {
        model: ProvidedServiceType,
        as: 'serviceTypes',
        attributes: ['id', 'serviceTypeId', 'minPrice', 'maxPrice'],
        include: [
          {
            model: ServiceType,
            as: 'serviceType',
            attributes: ['name'],
          },
        ],
      },
    ],
  });

  const [cancellationPolicy, availability, address] = await Promise.all([
    ProvidedServiceCancellationPolicy.findOne({
      where: { userId },
      attributes: ['id', 'startTime', 'endTime', 'cancellationFee'],
    }),

    ProvidedServiceAvailability.findAll({
      where: { userId },
      attributes: ['id', 'dayOfWeek'],
      include: [
        {
          model: ProvidedServiceAvailabilitySlot,
          as: 'slots',
          attributes: ['id', 'startTime', 'endTime'],
        },
      ],
    }),

    Address.findOne({
      where: { userId },
      attributes: ['id', 'zipcode', 'location', 'radius'],
    }),
  ]);

  const result = {
    providerService: providedService,
    cancellationPolicy,
    availability,
    address,
  };

  return {
    success: true,
    message: successMessage.DETAIL_MESSAGE('Provided service'),
    data: result,
  };
};

const checkAndUpdateProviderService = async (userId, body) => {
  const transaction = await ProvidedService.sequelize.transaction();

  try {
    const {
      note,
      serviceTypes = [],
      cancellationPolicy = {},
      availability = [],
      address = {},
    } = body;

    const providedService = await ProvidedService.findOne({
      where: { userId },
    });

    if (!providedService) {
      await transaction.rollback();
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Provided Service'),
      };
    }

    await providedService.update({ note }, { transaction });

    if (serviceTypes.length > 0) {
      for (const item of serviceTypes) {
        const [serviceTypeInstance] = await ProvidedServiceType.findOrCreate({
          where: {
            providedServiceId: providedService.id,
            serviceTypeId: item.serviceTypeId,
          },
          defaults: {
            minPrice: item.minPrice,
            maxPrice: item.maxPrice,
          },
          transaction,
        });

        if (!serviceTypeInstance._options.isNewRecord) {
          await serviceTypeInstance.update(
            {
              minPrice: item.minPrice,
              maxPrice: item.maxPrice,
            },
            { transaction }
          );
        }
      }
    }

    const existingPolicy = await ProvidedServiceCancellationPolicy.findOne({
      where: { userId },
    });

    const hasPolicyFields =
      cancellationPolicy.startTime ||
      cancellationPolicy.endTime ||
      cancellationPolicy.cancellationFee;

    if (hasPolicyFields) {
      const policyData = {
        userId,
        startTime: cancellationPolicy.startTime,
        endTime: cancellationPolicy.endTime,
        cancellationFee: cancellationPolicy.cancellationFee,
      };
      if (existingPolicy) {
        await existingPolicy.update(policyData, { transaction });
      } else {
        await ProvidedServiceCancellationPolicy.create(policyData, {
          transaction,
        });
      }
    }

    if (availability.length > 0) {
      for (const item of availability) {
        let av = await ProvidedServiceAvailability.findOne({
          where: {
            userId,
            dayOfWeek: item.dayOfWeek,
          },
          transaction,
        });

        if (!av) {
          av = await ProvidedServiceAvailability.create(
            { userId, dayOfWeek: item.dayOfWeek },
            { transaction }
          );
        }

        for (const slot of item.slots || []) {
          const [slotInstance] =
            await ProvidedServiceAvailabilitySlot.findOrCreate({
              where: {
                availabilityId: av.id,
                startTime: slot.startTime,
                endTime: slot.endTime,
              },
              defaults: {
                startTime: slot.startTime,
                endTime: slot.endTime,
              },
              transaction,
            });

          if (!slotInstance._options.isNewRecord) {
            await slotInstance.update(
              {
                startTime: slot.startTime,
                endTime: slot.endTime,
              },
              { transaction }
            );
          }
        }
      }
    }

    const existingAddress = await Address.findOne({ where: { userId } });
    const { zipcode, latitude, longitude, radius } = address;

    if (zipcode && latitude && longitude) {
      const location = {
        type: 'Point',
        coordinates: [parseFloat(longitude), parseFloat(latitude)],
      };

      if (existingAddress) {
        await existingAddress.update(
          {
            zipcode,
            radius,
            location,
          },
          { transaction }
        );
      } else {
        await Address.create(
          {
            userId,
            zipcode,
            radius,
            location,
          },
          { transaction }
        );
      }
    }

    await transaction.commit();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Provided Service'),
    };
  } catch (error) {
    await transaction.rollback();
    throw new Error(error);
  }
};

module.exports = {
  createProviderService,
  checkAndGetProviderService,
  checkAndUpdateProviderService,
};
