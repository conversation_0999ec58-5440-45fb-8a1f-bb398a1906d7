#!/bin/bash
set -e
set -o pipefail

echo "Installing pnpm via official install script..."

# Install pnpm for the webapp user
su -s /bin/bash webapp -c "curl -fsSL https://get.pnpm.io/install.sh | bash"

# Set PNPM_HOME
export PNPM_HOME="/home/<USER>/.local/share/pnpm"
export PATH="$PNPM_HOME:$PATH"

# Add to PATH for rest of this script
echo "export PNPM_HOME=$PNPM_HOME" >> /home/<USER>/.bashrc
echo 'export PATH=$PNPM_HOME:$PATH' >> /home/<USER>/.bashrc

# Check version
su -s /bin/bash webapp -c "$PNPM_HOME/pnpm --version"

# Install dependencies
su -s /bin/bash webapp -c "$PNPM_HOME/pnpm install --frozen-lockfile"

echo "pnpm installed and dependencies are set up successfully."
