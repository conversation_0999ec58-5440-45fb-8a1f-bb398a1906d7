const ServiceCategoryRepository = require('../../../../models/repositories/ServiceCategoryRepository');
const {
  genRes,
  errorMessage,
  resCode,
  errorTypes,
} = require('../../../../config/options');

exports.postServiceCategory = async (req, res) => {
  try {
    const { success, message, data } =
      await ServiceCategoryRepository.checkAndCreateServiceCategory(req.body);

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.putServiceCategory = async (req, res) => {
  try {
    const serviceCategoryId = req.params.id;

    const { success, message, data } =
      await ServiceCategoryRepository.checkAndUpdateServiceCategory(
        serviceCategoryId,
        req.body
      );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.getServiceCategoryListing = async (req, res) => {
  try {
    const { start = 0, limit = 10, search = null, serviceId } = req.query;

    const { message, data } =
      await ServiceCategoryRepository.getServiceCategoryAndCount({
        start: parseInt(start),
        limit: parseInt(limit),
        search,
        serviceId,
      });

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.patchChangeStatusServiceCategory = async (req, res) => {
  try {
    const { success, message } =
      await ServiceCategoryRepository.checkAndPatchServiceCategoryStatus(
        req.params.id,
        false
      );
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(
            resCode.HTTP_BAD_REQUEST,
            message,
            errorTypes.ACCESS_DENIED_EXCEPTION
          )
        );
    }
    return res
      .status(resCode.HTTP_CREATE)
      .json(genRes(resCode.HTTP_CREATE, { message }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};

exports.deleteServiceCategory = async (req, res) => {
  try {
    const { success, message } =
      await ServiceCategoryRepository.checkAndPatchServiceCategoryStatus(
        req.params.id,
        true
      );
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(
            resCode.HTTP_BAD_REQUEST,
            message,
            errorTypes.ACCESS_DENIED_EXCEPTION
          )
        );
    }
    return res
      .status(resCode.HTTP_CREATE)
      .json(genRes(resCode.HTTP_CREATE, { message }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};
