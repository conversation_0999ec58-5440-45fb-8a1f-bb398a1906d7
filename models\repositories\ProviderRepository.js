const {
  usersRoles,
  successMessage,
  errorMessage,
  serviceRequestProviderStatus,
  serviceRequestStatus,
} = require('../../config/options');
const {
  User,
  BusinessInformation,
  ProvidedService,
  ProvidedServiceType,
  ProvidedServiceAvailability,
  ProvidedServiceAvailabilitySlot,
  BusinessDocument,
  Address,
  ServiceType,
  ServiceRequest,
  ServiceRequestProvider,
} = require('..');
const sequelize = require('sequelize');
const { Op, literal, fn, col, where: sequelizeWhere } = sequelize;
const UserRepository = require('./UserRepository');

const getProviderAndCount = async (payload) => {
  const {
    start = 0,
    limit = 10,
    serviceTypeId = '',
    latitude,
    longitude,
  } = payload;

  try {
    const { count, rows } = await User.findAndCountAll({
      offset: Number(start),
      limit: Number(limit),
      distinct: true,
      where: {
        role: usersRoles.PROVIDER,
      },
      attributes: [
        'id',
        'firstName',
        'lastName',
        'profilePicture',
        'avgRating',
      ],
      include: [
        {
          model: Address,
          as: 'address',
          attributes: [
            'id',
            'zipcode',
            'city',
            'state',
            'addressType',
            'location',
            'radius',
          ],
          required: true,
          where: {
            location: {
              [Op.ne]: null,
            },
            [Op.and]: [
              literal(`
                ST_Distance(
                  "address"."location",
                  ST_MakePoint(${longitude}, ${latitude})::geography
                ) <= ("address"."radius" * 1609.34)
              `),
            ],
          },
        },
        {
          model: BusinessInformation,
          as: 'businessInfo',
          attributes: ['businessName', 'businessLogo'],
          include: [
            {
              model: BusinessDocument,
              as: 'documents',
              attributes: [
                'id',
                'filePath',
                'fileName',
                'fileCategory',
                'fileSize',
                'fileType',
              ],
            },
          ],
        },
        {
          model: ProvidedServiceAvailability,
          as: 'providedServiceAvailability',
          attributes: ['id', 'dayOfWeek'],
          include: [
            {
              model: ProvidedServiceAvailabilitySlot,
              as: 'slots',
              attributes: ['id', 'startTime', 'endTime'],
            },
          ],
        },
        {
          model: ProvidedService,
          as: 'providedService',
          include: [
            {
              model: ProvidedServiceType,
              as: 'serviceTypes',
              attributes: ['minPrice', 'maxPrice'],
              ...(serviceTypeId
                ? {
                    where: { serviceTypeId },
                  }
                : {}),
            },
          ],
        },
      ],
    });

    return {
      message: successMessage.DETAIL_MESSAGE('Provider'),
      data: {
        rows,
        pagination: {
          totalCount: count,
          start: Number(start),
          limit: Number(limit),
        },
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndGetProvider = async (providerId) => {
  try {
    const existingProvider = await User.findOne({
      where: {
        id: providerId,
        role: usersRoles.PROVIDER,
      },
      attributes: [
        'id',
        'firstName',
        'lastName',
        'profilePicture',
        'avgRating',
      ],
      include: [
        {
          model: BusinessInformation,
          as: 'businessInfo',
          attributes: ['businessName', 'businessLogo'],
          include: [
            {
              model: BusinessDocument,
              as: 'documents',
              attributes: [
                'id',
                'filePath',
                'fileName',
                'fileCategory',
                'fileSize',
                'fileType',
              ],
            },
          ],
        },
        {
          model: ProvidedServiceAvailability,
          as: 'providedServiceAvailability',
          attributes: ['id', 'dayOfWeek'],
          include: [
            {
              model: ProvidedServiceAvailabilitySlot,
              as: 'slots',
              attributes: ['id', 'startTime', 'endTime'],
            },
          ],
        },
        {
          model: ProvidedService,
          as: 'providedService',
          include: [
            {
              model: ProvidedServiceType,
              as: 'serviceTypes',
              attributes: ['minPrice', 'maxPrice'],
            },
          ],
        },
      ],
    });

    if (!existingProvider) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Provider'),
      };
    }

    return {
      success: true,
      data: existingProvider,
      message: successMessage.DETAIL_MESSAGE('Provider'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

const getAllProviderAndCount = async (payload) => {
  const { start = 0, limit = 10, search, fromDate, toDate } = payload;

  try {
    const whereConditions = [{ role: usersRoles.PROVIDER }];

    if (search) {
      whereConditions.push({
        [Op.or]: [
          { email: { [Op.iLike]: `%${search}%` } },
          { mobileNumber: { [Op.iLike]: `%${search}%` } },
          { '$businessInfo.businessName$': { [Op.iLike]: `%${search}%` } },
        ],
      });
    }

    if (fromDate && toDate) {
      whereConditions.push(
        sequelizeWhere(fn('DATE', col('User.createdAt')), {
          [Op.between]: [fromDate, toDate],
        })
      );
    }

    const { count, rows } = await User.findAndCountAll({
      offset: Number(start),
      limit: Number(limit),
      where: {
        [Op.and]: whereConditions,
      },
      order: [['createdAt', 'DESC']],
      attributes: [
        'id',
        'email',
        'firstName',
        'lastName',
        [
          literal(`CONCAT("User"."firstName", ' ', "User"."lastName")`),
          'fullName',
        ],
        'mobileNumber',
        'status',
        'createdAt',
      ],
      include: [
        {
          model: BusinessInformation,
          as: 'businessInfo',
          attributes: ['businessName', 'businessLogo'],
          required: !!search,
        },
        {
          model: ProvidedService,
          as: 'providedService',
          attributes: ['id'],
          separate: true,
          include: [
            {
              model: ProvidedServiceType,
              as: 'serviceTypes',
              attributes: ['id'],
              include: [
                {
                  model: ServiceType,
                  as: 'serviceType',
                  attributes: ['id', 'name'],
                },
              ],
            },
          ],
        },
      ],
    });

    return {
      message: successMessage.DETAIL_MESSAGE('Provider'),
      data: {
        rows,
        pagination: {
          totalCount: count,
          start: Number(start),
          limit: Number(limit),
        },
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndGetProviderById = async (providerId) => {
  try {
    const existingProvider = await User.findOne({
      where: {
        id: providerId,
        role: usersRoles.PROVIDER,
      },
      attributes: [
        'id',
        'firstName',
        'lastName',
        [
          literal(`CONCAT("User"."firstName", ' ', "User"."lastName")`),
          'fullName',
        ],
        'email',
        'mobileNumber',
        'profilePicture',
      ],
      include: [
        {
          model: Address,
          as: 'address',
          attributes: ['id', 'zipcode', 'radius'],
          required: false,
        },
        {
          model: BusinessInformation,
          as: 'businessInfo',
          attributes: [
            'id',
            'businessName',
            'businessLogo',
            'yearOfExperience',
          ],
          required: false,
          include: [
            {
              model: BusinessDocument,
              as: 'documents',
              attributes: [
                'id',
                'filePath',
                'fileName',
                'fileCategory',
                'fileSize',
                'fileType',
              ],
              required: false,
            },
          ],
        },
      ],
    });

    if (!existingProvider) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Provider'),
      };
    }

    return {
      success: true,
      data: existingProvider,
      message: successMessage.DETAIL_MESSAGE('Provider'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndPatchProviderStatus = async (id, isDelete) => {
  try {
    const { success, message } = await UserRepository.checkAndUpdateStatus(
      id,
      isDelete
    );
    return {
      success,
      message,
    };
  } catch (error) {
    throw new Error(error);
  }
};

const getDashboardServiceCount = async (user) => {
  const appointmentCount = await ServiceRequest.count({
    where: {
      providerId: user.id,
      status: serviceRequestStatus.CONFIRMED,
      [Op.and]: [
        sequelize.where(fn('DATE', col('scheduledAt')), {
          [Op.eq]: literal('CURRENT_DATE'),
        }),
      ],
    },
  });

  const serviceLeadsCount = await ServiceRequestProvider.count({
    where: {
      providerId: user.id,
      requestStatus: serviceRequestProviderStatus.PENDING,
    },
  });

  return {
    success: true,
    data: {
      appointmentCount,
      serviceLeadsCount,
    },
    message: successMessage.DETAIL_MESSAGE('Provider Dashboard Service Count'),
  };
};

module.exports = {
  getProviderAndCount,
  checkAndGetProvider,
  getAllProviderAndCount,
  checkAndGetProviderById,
  checkAndPatchProviderStatus,
  getDashboardServiceCount,
};
