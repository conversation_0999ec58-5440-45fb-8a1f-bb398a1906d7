paths:
  /admin/provider:
    get:
      tags:
        - "Provider"
      summary: "Get All Providers"
      description: "Retrieve a list of all providers with their email,mobileNumbers and rest details"
      operationId: "getAllProviders"
      security:
        - bearerAuth: []
      parameters:
        - name: start
          in: query
          required: false
          schema:
            type: integer
            default: 0
          description: Pagination start index (default is 0).
        - name: limit
          in: query
          required: false
          schema:
            type: integer
            default: 10
          description: Number of records to retrieve (default is 10).
        - name: "search"
          in: "query"
          description: "Search keyword to filter types by businessName,email or mobileNumber"
          required: false
          schema:
            type: string
        - name: fromDate
          in: query
          required: false
          schema:
            type: string
            example: "2024-01-01"
          description: >
            Filter providers created on or after this date (format: YYYY-MM-DD).
        - name: toDate
          in: query
          required: false
          schema:
            type: string
            example: "2024-12-31"
          description: >
            Filter providers created on or before this date (format: YYYY-MM-DD).
      responses:
        "200":
          description: "providers retrieved successfully"
        "400":
          description: "Invalid location parameters"
        "500":
          description: "Internal Server Error"

  /admin/provider/{id}:
    get:
      summary: Get provider details by ID
      description: "Get Provider List By its Id."
      operationId: "getProviderById"
      security:
        - bearerAuth: []
      tags:
        - "Provider"
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: The unique ID of the provider
      responses:
        '200':
          description: Provider details retrieved successfully
        '404':
          description: Provider not found

    patch:
      tags:
        - "Provider"
      summary: "Update Provider status by ID"
      description: "update a Provider status using its ID"
      operationId: "updateProviderStatus"
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: "ID of the  Provider to update status"
          schema:
            type: string
      responses:
        "201":
          description: "Provider status updated successfully"
        "400":
          description: "Invalid provider ID or status change not allowed"
        "404":
          description: "Provider not found"
        "500":
          description: "Internal Server Error"

    delete:
      tags:
        - "Provider"
      summary: "Delete a Provider"
      description: "Softdelete a Provider from the system using their ID."
      operationId: "deleteProvider"
      produces:
        - "application/json"
      parameters:
        - in: "path"
          name: "id"
          schema:
            type: string
          required: true
          description: "Unique identifier of the Provider"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Provider deleted successfully."
        "400":
          description: "Invalid Provider by ID."
        "500":
          description: "Internal server error."