'use strict';

const { dayOfWeek } = require('../config/options');

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('ProvidedServiceAvailability', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'User',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      dayOfWeek: {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: dayOfWeek.MONDAY,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('ProvidedServiceAvailability');
  },
};
