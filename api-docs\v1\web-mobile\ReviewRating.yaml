paths:
  /review:
    post:
      tags:
        - "ReviewRating"
      summary: "Create a new rating and review"
      description: "Allows a logged-in customer to submit a rating and review for a service provider. Requires a bearer token."
      operationId: "createRatingAndReview"
      security:
        - bearerAuth: []
      requestBody:
        description: "Payload with rating and review details"
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createRatingAndReview"
      produces:
        - "application/json"
      responses:
        "201":
          description: "Rating and review submitted successfully"
        "400":
          description: "Invalid input"
        "401":
          description: "Unauthorized"
        "500":
          description: "Internal Server Error"

    get:
      tags:
        - "ReviewRating"
      summary: "Get ratings and reviews"
      description: "Returns a paginated list of ratings and reviews submitted "
      operationId: "getRatingAndReviewOfProvider"
      security:
        - bearerAuth: []
      parameters:
        - name: start
          in: query
          required: false
          schema:
            type: integer
            default: 0
          description: "Pagination start index (offset)"
        - name: limit
          in: query
          required: false
          schema:
            type: integer
            default: 10
          description: "Number of reviews to fetch per page"
      produces:
        - application/json
      responses:
        "200":
          description: "List of ratings and reviews with pagination and average rating"
        "400":
          description: "Invalid request"
        "401":
          description: "Unauthorized"
        "500":
          description: "Internal Server Error"

  /review/statistics:
    get:
      tags:
        - "ReviewRating"
      summary: "Get average rating and rating distribution"
      description: "Returns the provider's average rating, total number of reviews,and a breakdown of ratings by each star level (1 to 5 stars).Requires provider authentication."
      operationId: "getRatingReviewStatisticsForProvider"
      security:
        - bearerAuth: []
      produces:
        - application/json
      responses:
        "200":
          description: "Statistics count for provider retrieved successfully"
        "400":
          description: "Invalid request"
        "401":
          description: "Unauthorized"
        "500":
          description: "Internal Server Error"

components:
  schemas:
    createRatingAndReview:
      type: object
      required:
        - serviceRequestId
        - rating
      properties:
        serviceRequestId:
          type: integer
          example: 25
          description: "ID of the serviceRequestId being reviewed"
        rating:
          type: number
          format: float
          example: 4.5
          description: "Rating out of 5"
        review:
          type: string
          example: "Great service and punctual!"
          description: "Optional review text"

