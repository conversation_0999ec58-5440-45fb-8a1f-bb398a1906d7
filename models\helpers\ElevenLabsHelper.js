const axios = require('axios');
const options = require('../../config/options');

const instance = axios.create();

instance.interceptors.request.use(
  (config) => {
    const newConfig = { ...config };
    newConfig.metadata = { startTime: new Date() };
    return newConfig;
  },
  (error) => Promise.reject(error)
);

instance.interceptors.response.use(
  (response) => {
    const newRes = { ...response };
    newRes.config.metadata.endTime = new Date();
    newRes.duration =
      newRes.config.metadata.endTime - newRes.config.metadata.startTime;
    return newRes;
  },
  (error) => {
    const newError = { ...error };
    if (newError.config && newError.config.metadata) {
      newError.config.metadata.endTime = new Date();
      newError.duration =
        newError.config.metadata.endTime - newError.config.metadata.startTime;
    }
    return Promise.reject(newError);
  }
);

/**
 * Generate speech audio from text using ElevenLabs API.
 * @param {Object} params
 * @param {string} params.text - The text to convert to speech.
 * @param {string} [params.voiceId] - The ElevenLabs voice ID to use.
 * @param {string} [params.outputFormat] - The output audio format (e.g., 'mp3_44100_128').
 * @returns {Promise<AxiosResponse>} - The Axios response (with stream).
 */
exports.generateSpeech = async ({
  text,
  voiceId,
  outputFormat = 'mp3_44100_128',
}) => {
  const baseUrl = options.elevenLabs.baseUrl;
  const resolvedVoiceId = voiceId || options.elevenLabs.defaultVoiceId;
  const url = `${baseUrl}/text-to-speech/${resolvedVoiceId}?output_format=${outputFormat}`;
  const headers = {
    'xi-api-key': process.env.ELEVEN_API_KEY,
    'Content-Type': 'application/json',
    Accept: 'audio/mpeg',
  };
  const reqOptions = {
    method: 'POST',
    url,
    headers,
    data: { text },
    responseType: 'stream',
  };
  return instance(reqOptions);
};
