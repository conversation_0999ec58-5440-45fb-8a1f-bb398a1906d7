const { Router } = require('express');

const router = Router();
const { checkSchema } = require('express-validator');

const {
  getCustomerListing,
  getCustomerById,
  patchChangeStatusCustomer,
  deleteCustomer,
} = require('../../../../controllers/api/v1/admin/Customer');

const {
  requestValidator,
} = require('../../../../models/helpers/ErrorHandleHelper');
const {
  customerDateQueryValidation,
} = require('../../../../schema-validation/admin/Customer');

router.get(
  '/',
  checkSchema(customerDateQueryValidation),
  requestValidator,
  getCustomerListing
);

router.get('/:id', getCustomerById);

router.patch('/:id', patchChangeStatusCustomer);

router.delete('/:id', deleteCustomer);

module.exports = router;
