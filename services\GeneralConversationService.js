const { generateResponse } = require('./LLMService');
const ChatMemoryService = require('./ChatMemoryService');
const options = require('../config/options');

exports.handleGeneralConversation = async (message, sessionId) => {
  try {
    ChatMemoryService.appendToHistory(
      sessionId,
      options.chatRole.USER,
      message
    );
    const historyText = ChatMemoryService.getFormattedHistory(sessionId);

    const response = await generateResponse(message, historyText);
    ChatMemoryService.appendToHistory(
      sessionId,
      options.chatRole.ASSISTANT,
      response
    );
    return { message: response, data: null };
  } catch (error) {
    return {
      message:
        "I'm sorry, I'm having trouble processing your request right now. Please try again.",
      data: null,
    };
  }
};
