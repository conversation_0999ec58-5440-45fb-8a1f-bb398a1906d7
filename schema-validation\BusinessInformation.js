exports.createOrUpdateBusinessInfo = {
  businessLogo: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'businessLogo  must be string',
    },
  },
  businessName: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'businessName must be string',
    },
  },
  about: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'about must be string',
    },
  },
  yearOfExperience: {
    in: ['body'],
    optional: true,
    trim: true,
    notEmpty: true,
    errorMessage: 'yearOfExperience cannot be empty',
    isInt: {
      errorMessage: 'yearOfExperience must be an integer',
    },
  },
  hasCertificationLicense: {
    in: ['body'],
    optional: true,
    isBoolean: {
      errorMessage: 'hasCertificationLicense must be a boolean',
    },
  },
  isInsured: {
    in: ['body'],
    optional: true,
    isBoolean: {
      errorMessage: 'isInsured must be a boolean',
    },
  },
  hasToolset: {
    in: ['body'],
    optional: true,
    isBoolean: {
      errorMessage: 'hasToolset must be a boolean',
    },
  },
};
