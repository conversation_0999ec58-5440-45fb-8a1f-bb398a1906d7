const {
  ServiceRequest,
  Address,
  Vehicle,
  User,
  ServiceRequestOption,
  ServiceOption,
  BusinessInformation,
  ServiceRequestProvider,
  SubService,
  ServiceType,
} = require('..');
const {
  successMessage,
  errorMessage,
  serviceRequestStatus,
  serviceRequestProviderStatus,
  serviceAppointmentUpdateType,
  usersRoles,
} = require('../../config/options');
const AddressRepository = require('./AddressRepository');
const { patchAppointmentByType } = require('../helpers/ServiceRequestHelper');
const sequelize = require('sequelize');
const { Op } = sequelize;

const fetchServiceRequestDetails = async (id, userId = null) => {
  const where = { id };
  if (userId) where.userId = userId;

  const serviceRequest = await ServiceRequest.findOne({
    where,
    attributes: [
      'id',
      'userId',
      'vehicleId',
      'addressId',
      'scheduledAt',
      'propertyLocation',
      'otherLocation',
      'specialInstruction',
      'serviceDuration',
      'note',
      'createdAt',
    ],
    include: [
      {
        model: Address,
        as: 'address',
        required: false,
        attributes: [
          'id',
          'addressLine1',
          'zipcode',
          'location',
          'city',
          'state',
          'addressType',
        ],
      },
      {
        model: Vehicle,
        as: 'vehicle',
        required: false,
        attributes: [
          'id',
          'industry',
          'model',
          'vehicleTrim',
          'engine',
          'year',
        ],
      },
      {
        model: User,
        as: 'user',
        required: false,
        attributes: [
          'id',
          'firstName',
          'lastName',
          'email',
          'countryCode',
          'mobileNumber',
          'profilePicture',
        ],
      },
      {
        model: ServiceRequestOption,
        as: 'serviceOptions',
        attributes: ['id'],
        required: false,
        include: [
          {
            model: ServiceOption,
            as: 'serviceOption',
            required: false,
            attributes: ['id', 'name'],
          },
        ],
      },
    ],
  });

  return serviceRequest || null;
};

const createServiceRequest = async (userId, body) => {
  try {
    if (body.addressId) {
      const addressExists = await Address.findOne({
        where: { id: body.addressId, userId },
      });

      if (!addressExists) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST('Selected Address'),
        };
      }
    } else if (body.address?.zipcode) {
      const result = await AddressRepository.createAddress(
        userId,
        body.address
      );
      if (!result.success) {
        return {
          success: false,
          message: result.message,
        };
      }
      body.addressId = result.data.id;
    }

    const requestPayload = {
      ...body,
      userId,
      serviceOptions: Array.isArray(body.serviceOptionIds)
        ? body.serviceOptionIds.map((id) => ({ serviceOptionId: id }))
        : [],
      serviceProviders: Array.isArray(body.providerIds)
        ? body.providerIds.map((id) => ({ providerId: id }))
        : [],
    };

    const newRequest = await ServiceRequest.create(requestPayload, {
      include: [
        {
          model: ServiceRequestOption,
          as: 'serviceOptions',
        },
        {
          model: ServiceRequestProvider,
          as: 'serviceProviders',
        },
      ],
    });

    const fullDetails = await fetchServiceRequestDetails(newRequest.id);

    return {
      success: true,
      message: successMessage.CREATED_MESSAGE('Service Request'),
      data: fullDetails,
    };
  } catch (error) {
    throw new Error(error);
  }
};

const getServiceRequestById = async (id) => {
  try {
    const serviceRequest = await fetchServiceRequestDetails(id);

    if (!serviceRequest) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Service Request'),
        data: null,
      };
    }

    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE('Service Request'),
      data: serviceRequest,
    };
  } catch (error) {
    throw new Error(error);
  }
};

const getServiceRequestAndCount = async ({
  userId,
  start = 0,
  limit = 10,
  status,
}) => {
  try {
    const where = {
      userId,
      ...(status && {
        status,
      }),
      ...(!status && {
        status: [
          serviceRequestStatus.AVAILABLE,
          serviceRequestStatus.PENDING,
          serviceRequestStatus.CANCELLED,
          serviceRequestStatus.CLOSED,
          serviceRequestStatus.RECEIVED,
          serviceRequestStatus.ACTIVE,
        ],
      }),
    };

    const include = [
      {
        model: Address,
        as: 'address',
        attributes: ['id', 'zipcode', 'city', 'state', 'addressType'],
      },
      {
        model: Vehicle,
        as: 'vehicle',
        attributes: ['id', 'industry', 'model', 'year'],
      },
      {
        model: User,
        as: 'user',
        attributes: [
          'id',
          'firstName',
          'lastName',
          'email',
          'mobileNumber',
          'profilePicture',
        ],
        include: [
          {
            model: BusinessInformation,
            as: 'businessInfo',
            attributes: ['businessName', 'businessLogo'],
          },
        ],
      },
      {
        model: ServiceRequestOption,
        as: 'serviceOptions',
        attributes: ['id'],
        include: [
          {
            model: ServiceOption,
            as: 'serviceOption',
            attributes: ['id', 'name'],
          },
        ],
      },
    ];

    const [rows, totalCount] = await Promise.all([
      ServiceRequest.findAll({
        where,
        offset: Number(start),
        limit: Number(limit),
        order: [['createdAt', 'DESC']],
        attributes: [
          'id',
          'scheduledAt',
          'propertyLocation',
          'otherLocation',
          'specialInstruction',
          'serviceDuration',
          'note',
          'status',
          'createdAt',
        ],
        include,
      }),
      ServiceRequest.count({ where }),
    ]);

    return {
      message: successMessage.DETAIL_MESSAGE('Service Request'),
      data: {
        rows,
        pagination: {
          totalCount,
          start: Number(start),
          limit: Number(limit),
        },
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

const getServiceRequestAndCountByProvider = async ({
  providerId,
  start = 0,
  limit = 10,
  requestStatus,
}) => {
  try {
    const serviceProviderWhere = {
      providerId,
      ...(requestStatus && { requestStatus }),
      ...(!requestStatus && {
        requestStatus: [
          serviceRequestProviderStatus.PENDING,
          serviceRequestProviderStatus.ACCEPTED,
        ],
      }),
    };

    const include = [
      {
        model: ServiceRequest,
        as: 'serviceRequest',
        attributes: [
          'id',
          'scheduledAt',
          'propertyLocation',
          'otherLocation',
          'specialInstruction',
          'serviceDuration',
          'note',
          'status',
          'createdAt',
        ],
        include: [
          {
            model: Address,
            as: 'address',
            required: false,
            attributes: [
              'id',
              'zipcode',
              'city',
              'state',
              'addressType',
              'location',
            ],
          },
          {
            model: Vehicle,
            as: 'vehicle',
            required: false,
            attributes: [
              'id',
              'industry',
              'model',
              'year',
              'vehicleTrim',
              'vehicleType',
              'engine',
            ],
          },
          {
            model: User,
            as: 'user',
            required: false,
            attributes: [
              'id',
              'firstName',
              'lastName',
              'email',
              'mobileNumber',
              'profilePicture',
            ],
          },
          {
            model: ServiceRequestOption,
            as: 'serviceOptions',
            required: false,
            attributes: ['id'],
            separate: true,
            include: [
              {
                model: ServiceOption,
                as: 'serviceOption',
                attributes: ['id', 'name'],
                required: false,
                include: [
                  {
                    model: SubService,
                    as: 'subService',
                    attributes: ['id', 'name'],
                    required: false,
                    include: [
                      {
                        model: ServiceType,
                        as: 'serviceType',
                        attributes: ['id', 'name'],
                        required: false,
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
    ];

    const { count: totalCount, rows } =
      await ServiceRequestProvider.findAndCountAll({
        where: serviceProviderWhere,
        offset: Number(start),
        limit: Number(limit),
        order: [['actionAt', 'DESC']],
        attributes: ['id', 'providerId', 'requestStatus', 'actionAt'],
        include,
      });

    return {
      message: successMessage.DETAIL_MESSAGE('Service Requests for Provider'),
      data: {
        rows,
        pagination: {
          totalCount,
          start: Number(start),
          limit: Number(limit),
        },
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndPatchAcceptRejectServiceRequest = async (id, body) => {
  try {
    const { providerId, requestStatus } = body;

    const lead = await ServiceRequestProvider.findOne({
      where: { id, providerId },
      attributes: ['id', 'requestId', 'requestStatus', 'actionAt'],
    });

    if (!lead) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Lead'),
        data: null,
      };
    }

    lead.requestStatus = requestStatus;

    lead.actionAt = new Date();
    await lead.save();

    if (requestStatus === serviceRequestProviderStatus.ACCEPTED) {
      await ServiceRequestProvider.update(
        {
          requestStatus: serviceRequestProviderStatus.CLOSED,
          actionAt: new Date(),
        },
        {
          where: {
            requestId: lead.requestId,
            id: { [Op.ne]: id },
          },
        }
      );
    }

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Lead status'),
      data: lead,
    };
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndPatchServiceRequestAppointment = async (payload) => {
  try {
    const {
      id,
      operation,
      cancellationReason,
      rescheduleReason,
      scheduledAt,
      userRole,
      loggedUserId,
    } = payload;

    const currentTime = new Date();
    const serviceRequest = await ServiceRequest.findByPk(id);

    if (!serviceRequest) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Service Request'),
        data: null,
      };
    }

    if (
      [
        serviceAppointmentUpdateType.CONFIRM,
        serviceAppointmentUpdateType.START,
        serviceAppointmentUpdateType.CLOSE,
      ].includes(operation) &&
      userRole !== usersRoles.PROVIDER
    ) {
      return {
        success: false,
        message: errorMessage.UNAUTHORIZED_ACCESS,
        data: null,
      };
    }

    try {
      patchAppointmentByType({
        serviceRequest,
        operation,
        cancellationReason,
        rescheduleReason,
        scheduledAt,
        loggedUserId,
        currentTime,
        serviceAppointmentUpdateType,
        serviceRequestStatus,
        userRole,
      });
    } catch (err) {
      return {
        success: false,
        message: err.message,
        data: null,
      };
    }

    // TODO: Handle notification to customer and provider

    await serviceRequest.save();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE(
        'Service Request appointment'
      ),
      data: serviceRequest,
    };
  } catch (error) {
    throw new Error(error);
  }
};

module.exports = {
  createServiceRequest,
  getServiceRequestById,
  getServiceRequestAndCount,
  getServiceRequestAndCountByProvider,
  checkAndPatchAcceptRejectServiceRequest,
  checkAndPatchServiceRequestAppointment,
};
