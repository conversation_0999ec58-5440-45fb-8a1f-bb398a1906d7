paths:
  /provider:
    get:
      tags:
        - "Provider"
      summary: "Get Nearby Providers"
      description: "Retrieve a list of nearby providers based on latitude, longitude, and optional radius."
      operationId: "getNearbyProviders"
      security:
        - bearerAuth: []
      parameters:
        - name: latitude
          in: query
          required: true
          schema:
            type: number
            format: float
          description: Latitude of the user's current location.
        - name: longitude
          in: query
          required: true
          schema:
            type: number
            format: float
          description: Longitude of the user's current location.
        - name: radius
          in: query
          required: false
          schema:
            type: number
            format: float
            default: 10
          description: Search radius in miles (default is 10).
        - name: start
          in: query
          required: false
          schema:
            type: integer
            default: 0
          description: Pagination start index (default is 0).
        - name: limit
          in: query
          required: false
          schema:
            type: integer
            default: 10
          description: Number of records to retrieve (default is 10).
        - name: serviceTypeId
          in: query
          required: false
          schema:
            type: string
            format: uuid
          description: Optional service type id to filter providers.
      responses:
        "200":
          description: "Nearby providers retrieved successfully"
        "400":
          description: "Invalid location parameters"
        "500":
          description: "Internal Server Error"

  /provider/{id}:
    get:
      summary: Get provider details by ID
      description: "Get Provider List By its Id."
      operationId: "getProviderById"
      security:
        - bearerAuth: []
      tags:
        - "Provider"
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: The unique ID of the provider
      responses:
        "200":
          description: Provider details retrieved successfully
        "404":
          description: Provider not found

  /provider/dashboard/service-count:
    get:
      tags:
        - "Provider"
      summary: "Get dashboard service count"
      description: "Retrieve counts for dashboard service such as appointment count and service leads count."
      operationId: "getProviderDashboardServiceCount"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Dashboard service count retrieved successfully"
        "500":
          description: "Internal Server Error"
