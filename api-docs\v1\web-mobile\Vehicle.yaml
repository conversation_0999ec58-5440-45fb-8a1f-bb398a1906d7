paths:
  /vehicle:
    post:
      tags:
        - "Vehicle"
      summary: "Create a new vehicle entry"
      description: "Allows a logged-in user to create a vehicle profile. Requires a bearer token."
      operationId: "createVehicle"
      security:
        - bearerAuth: [ ]
      requestBody:
        description: "Payload with vehicle details"
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createOrUpdateVehicle"
      produces:
        - "application/json"
      responses:
        "201":
          description: "Vehicle created successfully"
        "400":
          description: "Invalid input"
        "500":
          description: "Internal Server Error"

    get:
      tags:
        - "Vehicle"
      summary: "Get list of vehicles"
      description: "Retrieves a paginated list of vehicles for the logged-in user. Requires a bearer token."
      operationId: "listVehicles"
      security:
        - bearerAuth: [ ]
      parameters:
        - name: start
          in: query
          required: false
          description: start number
          schema:
            type: integer
            example: 1
        - name: limit
          in: query
          required: false
          description: Number of items per page 
          schema:
            type: integer
            example: 10
      produces:
        - "application/json"
      responses:
        "200":
          description: "List of vehicles retrieved successfully"
        "401":
          description: "Unauthorized"
        "500":
          description: "Internal Server Error"

  /vehicle/{id}:
    put:
      tags:
        - "Vehicle"
      summary: "Update an existing vehicle entry"
      description: "Allows a logged-in user to update an existing vehicle profile by ID. Requires a bearer token."
      operationId: "updateVehicle"
      security:
        - bearerAuth: [ ]
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the vehicle to update
          schema:
            type: integer
      requestBody:
        description: "Payload with updated vehicle details"
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createOrUpdateVehicle"
      produces:
        - "application/json"
      responses:
        "200":
          description: "Vehicle updated successfully"
        "400":
          description: "Invalid input or vehicle not found"
        "500":
          description: "Internal Server Error"

    get:
      tags:
        - "Vehicle"
      summary: "Get vehicle details by ID"
      description: "Fetch a single vehicle's details using its ID. Requires a bearer token."
      operationId: "getVehicleById"
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: "ID of the vehicle to retrieve"
          schema:
            type: integer
      responses:
        "200":
          description: "Vehicle details retrieved successfully"
        "400":
          description: "Invalid vehicle ID"
        "404":
          description: "Vehicle not found"
        "500":
          description: "Internal Server Error"

    delete:
      tags:
        - "Vehicle"
      summary: "Delete vehicle by ID"
      description: "Soft deletes a vehicle using its ID. Requires a bearer token."
      operationId: "deleteVehicle"
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: "ID of the vehicle to delete"
          schema:
            type: integer
      responses:
        "201":
          description: "Vehicle deleted successfully"
        "400":
          description: "Invalid vehicle ID or deletion not allowed"
        "404":
          description: "Vehicle not found"
        "500":
          description: "Internal Server Error"

  /vehicle/dropdown/options:
    get:
      tags:
        - "Vehicle"
      summary: "Get dynamic vehicle dropdown options"
      description: >
        Returns dropdown options for vehicle fields like Type, Year, Make, Model, Trim, Engine, Drivetrain, etc.
        Options are optionally filtered based on query params to support dependent dropdowns.
      operationId: "getVehicleOptions"
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: type
          schema:
            type: string
          description: "Filter makes based on selected vehicle type"
        - in: query
          name: year
          schema:
            type: string
          description: "Filter makes/models based on selected year"
        - in: query
          name: make
          schema:
            type: string
          description: "Filter models based on selected make"
        - in: query
          name: model
          schema:
            type: string
          description: "Filter trims based on selected model"
        - in: query
          name: trim
          schema:
            type: string
          description: "Filter engines based on selected trim"
      responses:
        "200":
          description: "Dropdown options fetched successfully"
        "500":
          description: "Internal Server Error"

  /vehicle/vin:
    post:
      tags:
        - "Vehicle"
      summary: "Create a vehicle from VIN"
      description: "Allows a logged-in user to decode a VIN and create a vehicle entry based on decoded details. Requires a bearer token."
      operationId: "createVehicleFromVin"
      security:
        - bearerAuth: [ ]
      requestBody:
        description: "Payload with vehicle details"
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createVehicleFromVinRequest"
      produces:
        - application/json
      responses:
        "201":
          description: "Vehicle created successfully from VIN"
        "400":
          description: "Invalid input or VIN decoding failure"
        "500":
          description: "Internal Server Error"

components:
  schemas:
    createOrUpdateVehicle:
      type: object
      properties:
        vehicleType:
          type: string
          example: "SUV"
        year:
          type: integer
          example: 2020
        industry:
          type: string
          example: "Toyota"
        model:
          type: string
          example: "RAV4"
        vehicleTrim:
          type: string
          example: "XLE"
        engine:
          type: string
          example: "2.5L I4"
        driveTrain:
          type: string
          example: "AWD"
        milage:
          type: integer
          example: 35000
        vin:
          type: string
          example: "1HGCM82633A004352"
        plateNumber:
          type: string
          example: "AB123CD"
        state:
          type: string
          example: "California"
      required:
        - vehicleType

    createVehicleFromVinRequest:
      type: object
      required:
        - vin
      properties:
        vin:
          type: string
          description: 17-character Vehicle Identification Number (VIN)
          example: JM1GJ1V56F1184000
