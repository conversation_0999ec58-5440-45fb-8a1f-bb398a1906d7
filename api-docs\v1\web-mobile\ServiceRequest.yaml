paths:
  /service-request:
    post:
      tags:
        - "ServiceRequest"
      summary: "Add Service Request"
      description: "Allows a logged-in user to create a service request with optional address"
      operationId: "addServiceRequest"
      security:
        - bearerAuth: []
      requestBody:
        description: "Service request payload with optional nested address object"
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/addServiceRequest"
      produces:
        - "application/json"
      responses:
        "201":
          description: "Service request created successfully"
        "400":
          description: "Invalid input"
        "500":
          description: "Internal Server Error"

    get:
      tags:
        - "ServiceRequest"
      summary: "List Service Requests"
      description: "Fetch paginated list of service requests with related user, vehicle, address, and service options."
      operationId: "getServiceRequestList"
      security:
        - bearerAuth: []
      parameters:
        - name: start
          in: query
          description: "Starting index for pagination"
          required: false
          schema:
            type: integer
            default: 0
        - name: limit
          in: query
          description: "Maximum number of results to return"
          required: false
          schema:
            type: integer
            default: 10
        - name: status
          in: query
          description: "Status of service request"
          required: false
          schema:
            type: string
            enum: [available, pending, received]
            default: "pending"
      responses:
        "200":
          description: "Service requests fetched successfully"
        "500":
          description: "Internal Server Error"

  /service-request/lead:
    get:
      tags:
        - "ServiceRequest"
      summary: "List Service Request based on provider"
      description: "Fetch paginated list of service requests with related user, vehicle, address, and service options."
      operationId: "getServiceRequestListByProvider"
      security:
        - bearerAuth: []
      parameters:
        - name: start
          in: query
          description: "Starting index for pagination"
          required: false
          schema:
            type: integer
            default: 0
        - name: limit
          in: query
          description: "Maximum number of results to return"
          required: false
          schema:
            type: integer
            default: 10
        - name: status
          in: query
          description: "Status of service request"
          required: false
          schema:
            type: string
            enum: [pending]
            default: "pending"
      responses:
        "200":
          description: "Service requests fetched successfully"
        "500":
          description: "Internal Server Error"

  /service-request/lead/{id}:
      patch:
        tags:
          - "ServiceRequest"
        summary: "Update Lead Status"
        description: "Update the status of a lead (service request provider) to accepted or rejected."
        operationId: "updateLeadStatus"
        security:
          - bearerAuth: []
        parameters:
          - in: path
            name: id
            required: true
            schema:
              type: integer
            description: Service request lead ID
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  requestStatus:
                    type: string
                    enum: [accepted, rejected]
                    description: Status to update (accepted or rejected)
                required:
                  - requestStatus
        responses:
          "200":
            description: "Lead status updated successfully"
          "400":
            description: "Invalid input or missing rejection comment"
          "404":
            description: "Lead not found"
          "500":
            description: "Internal Server Error"
  /service-request/{id}:
    get:
      tags:
        - "ServiceRequest"
      summary: "Get Service Request by ID"
      description: "Fetch a service request's details by its ID. Requires authentication."
      operationId: "getServiceRequestById"
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: "ID of the service request to fetch"
          schema:
            type: integer
      produces:
        - "application/json"
      responses:
        "200":
          description: "Service request details fetched successfully"
        "404":
          description: "Service request not found"
        "500":
          description: "Internal Server Error"

  /service-request/{id}/appointment:
    patch:
      tags:
        - "ServiceRequest"
      summary: "Cancel or Reschedule Service Request Appointment."
      description: "Cancel or reschedule a service request appointment."
      operationId: "updateServiceRequestAppointment"
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: "ID of the service request to update"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                operation:
                  type: string
                  enum: [cancel, confirm, reschedule, start, close]
                  description: "Type of appointment update"
                cancellationReason:
                  type: string
                  description: "Reason for cancellation (required if type is cancel)"
                rescheduleReason:
                  type: string
                  description: "Reason for rescheduling (required if type is reschedule)"
                scheduledAt:
                  type: string
                  format: date-time
                  description: "New scheduled date and time (required if type is reschedule)"
              required:
                - type
      responses:
        "200":
          description: "Service request appointment updated successfully"
        "400":
          description: "Invalid input"
        "404":
          description: "Service request not found"
        "500":
          description: "Internal Server Error"
components:
  schemas:
    addServiceRequest:
      type: object
      required:
        - vehicleId
      properties:
        vehicleId:
          type: integer
          example: 2
        scheduledAt:
          type: string
          format: date-time
          example: "2025-06-18T09:00:00Z"
        propertyLocation:
          type: string
          example: "garage"
          enum: [garage, doorstep]
        otherLocation:
          type: string
          example: "Building 5, Near Main Circle"
        specialInstruction:
          type: string
          example: "Handle with care"
        serviceDuration:
          type: string
          example: "urgent"
          enum: [urgent, scheduled]
        addressId:
          type: integer
          example: 2
        note:
          type: string
          example: "data"
        serviceOptionIds:
          type: array
          items:
            type: string
            format: uuid
          example:
            - "e3b0c442-98fc-1c14-9af4-0e61c4e9e614"
            - "7d8b2c99-1c43-422b-8a52-4ac7e97c1f35"
        providerIds:
          type: array
          items:
            type: integer
          example: [12, 15]
        address:
          type: object
          description: "Optional address object"
          properties:
            zipcode:
              type: string
              example: "560001"
            city:
              type: string
              example: "Bangalore"
            state:
              type: string
              example: "Karnataka"
            addressType:
              type: string
              example: "home"
              enum: [home, work, other]