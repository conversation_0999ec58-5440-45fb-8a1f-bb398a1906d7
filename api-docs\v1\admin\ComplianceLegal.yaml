paths:
  /admin/compliance-legal:
    post:
      tags:
        - "ComplianceLegal"
      summary: "Create a new Compliance Legal entry"
      description: "Add a new Compliance Legal record with the provided details."
      operationId: "createComplianceLegal"
      requestBody:
        description: "Payload containing Compliance Legal details."
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createComplianceLegal"
        required: true
      produces:
        - "application/json"
      parameters: []
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Compliance Legal entry created successfully."
        "400":
          description: "Invalid request payload."
        "500":
          description: "Internal server error."

    get:
      tags:
        - "ComplianceLegal"
      summary: "Get Compliance Legal list"
      description: "Retrieve a list of all Compliance Legal records."
      operationId: "getComplianceLegalList"
      produces:
        - "application/json"
      parameters:
        - in: query
          name: search
          schema:
            type: string
          description: Filter results by title
        - in: query
          name: start
          schema:
            type: integer
            default: 0
          description: Pagination offset
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          description: Number of items per page
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "List of Compliance Legal entries retrieved successfully."
        "401":
          description: "Unauthorized access."
        "500":
          description: "Internal server error."

  /admin/compliance-legal/{id}:
    get:
      tags:
        - "ComplianceLegal"
      summary: "Get Compliance Legal entry by ID"
      description: "Retrieve details of a specific Compliance Legal record by its ID."
      operationId: "getComplianceLegalById"
      produces:
        - "application/json"
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: "ID of the Compliance Legal entry"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Compliance Legal entry details retrieved successfully."
        "404":
          description: "Compliance Legal entry not found."
        "500":
          description: "Internal server error."

    put:
      tags:
        - "ComplianceLegal"
      summary: "Update Compliance Legal entry"
      description: "Update an existing Compliance Legal record by its ID."
      operationId: "updateComplianceLegal"
      produces:
        - "application/json"
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: "ID of the Compliance Legal entry to update"
      requestBody:
        description: "Payload with updated Compliance Legal data."
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/updateComplianceLegal"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Compliance Legal entry updated successfully."
        "404":
          description: "Compliance Legal entry not found."
        "400":
          description: "Invalid request payload."
        "500":
          description: "Internal server error."


components:
  schemas:
    createComplianceLegal:
      type: object
      properties:
        title:
          type: string
          example: "FSSAI Compliance Policy"
        description:
          type: string
          example: "This policy outlines legal obligations for food safety compliance."
      required:
        - title
        - description
    updateComplianceLegal:
      type: object
      properties:
        description:
          type: string
      example:
        description: "Updated description for the compliance legal entry."
      required:
        - description