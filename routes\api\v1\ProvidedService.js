const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();

const ProvidedServiceControl = require('../../../controllers/api/v1/ProvidedService');
const ProvidedServiceSchema = require('../../../schema-validation/ProvidedService');
const ErrorHandleHelper = require('../../../models/helpers/ErrorHandleHelper');

router.post(
  '/',
  checkSchema(ProvidedServiceSchema.validateProvidedService),
  ErrorHandleHelper.requestValidator,
  ProvidedServiceControl.postProvidedService
);

router.get('/', ProvidedServiceControl.getProvidedService);

router.put(
  '/',
  checkSchema(ProvidedServiceSchema.validateProvidedService),
  ErrorHandleHelper.requestValidator,
  ProvidedServiceControl.putProvidedService
);

module.exports = router;
