const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();

const AuthHandler = require('../../../models/helpers/AuthHelper');
const ServiceRequestControl = require('../../../controllers/api/v1/ServiceRequest');
const ServiceRequestSchema = require('../../../schema-validation/ServiceRequest');
const ErrorHandleHelper = require('../../../models/helpers/ErrorHandleHelper');
const { usersRoles } = require('../../../config/options');

router.post(
  '/',
  AuthHandler.authenticateJWT([usersRoles.CUSTOMER]),
  checkSchema(ServiceRequestSchema.addServiceRequest),
  ErrorHandleHelper.requestValidator,
  ServiceRequestControl.postServiceRequest
);

router.get(
  '/lead',
  AuthHandler.authenticateJWT([usersRoles.PROVIDER]),
  ServiceRequestControl.getServiceRequestListingByProvider
);

router.patch(
  '/lead/:id',
  AuthHandler.authenticateJWT([usersRoles.PROVIDER]),
  checkSchema(ServiceRequestSchema.updateLeadStatus),
  ErrorHandleHelper.requestValidator,
  ServiceRequestControl.updateLeadStatus
);

router.get(
  '/:id',
  AuthHandler.authenticateJWT([usersRoles.CUSTOMER, usersRoles.PROVIDER]),
  ServiceRequestControl.getServiceRequestById
);

router.get(
  '/',
  AuthHandler.authenticateJWT([usersRoles.CUSTOMER]),
  ServiceRequestControl.getServiceRequestListing
);

router.patch(
  '/:id/appointment',
  AuthHandler.authenticateJWT([usersRoles.CUSTOMER, usersRoles.PROVIDER]),
  checkSchema(ServiceRequestSchema.updateAppointment),
  ErrorHandleHelper.requestValidator,
  ServiceRequestControl.updateAppointment
);

module.exports = router;
