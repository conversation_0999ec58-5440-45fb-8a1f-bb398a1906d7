const DealsOnWheelsRepository = require('../../../../models/repositories/DealsOnWheelsRepository');
const { genRes, errorMessage, resCode } = require('../../../../config/options');

exports.createDealsOnWheels = async (req, res) => {
  try {
    const { success, message, data } =
      await DealsOnWheelsRepository.createDealsOnWheels(req.body);

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.updateDealsOnWheels = async (req, res) => {
  try {
    const { id } = req.params;

    const { success, message, data } =
      await DealsOnWheelsRepository.updateDealsOnWheels(id, req.body);

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.updateDealsOnWheelsStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const { success, message, data } =
      await DealsOnWheelsRepository.updateDealsOnWheels(id, { status });

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.getDealsOnWheelsList = async (req, res) => {
  try {
    const { start = 0, limit = 10 } = req.query;

    const { message, data } =
      await DealsOnWheelsRepository.getDealsOnWheelsAndCount({
        start: parseInt(start),
        limit: parseInt(limit),
      });

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};
