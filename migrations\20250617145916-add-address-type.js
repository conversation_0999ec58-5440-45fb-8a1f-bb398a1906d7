'use strict';

const { addressType } = require('../config/options');

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('Address', 'addressType', {
      type: Sequelize.STRING,
      allowNull: false,
      defaultValue: addressType.HOME,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('Address', 'addressType');
  },
};
