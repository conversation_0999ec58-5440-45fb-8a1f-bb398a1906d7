'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('Address', 'addressLine1', {
      type: Sequelize.STRING,
      allowNull: true,
    });
    await queryInterface.addColumn('Address', 'category', {
      type: Sequelize.STRING,
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('Address', 'addressLine1');
    await queryInterface.removeColumn('Address', 'category');
  },
};
