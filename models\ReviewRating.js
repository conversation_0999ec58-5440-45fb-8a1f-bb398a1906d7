module.exports = (sequelize, DataTypes) => {
  const ReviewRating = sequelize.define(
    'ReviewRating',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      rating: {
        type: DataTypes.DECIMAL(3, 2),
        allowNull: false,
      },
      review: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
    },
    {
      tableName: 'ReviewRating',
      timestamps: true,
    }
  );

  ReviewRating.associate = function (models) {
    ReviewRating.belongsTo(models.ServiceRequest, {
      foreignKey: 'serviceRequestId',
      as: 'serviceRequest',
      onDelete: 'CASCADE',
    });

    ReviewRating.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE',
    });
  };

  return ReviewRating;
};
