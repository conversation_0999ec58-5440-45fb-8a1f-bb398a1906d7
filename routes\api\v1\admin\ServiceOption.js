const { Router } = require('express');

const router = Router();
const { checkSchema } = require('express-validator');

const {
  postServiceOption,
  putServiceOption,
  getServiceOptionList,
  patchChangeStatusServiceOption,
  deleteServiceOption,
} = require('../../../../controllers/api/v1/admin/ServiceOption');

const {
  requestValidator,
} = require('../../../../models/helpers/ErrorHandleHelper');
const {
  createAndUpdateServiceOption,
  updateServiceOption,
} = require('../../../../schema-validation/admin/ServiceOption');

router.post(
  '/',
  checkSchema(createAndUpdateServiceOption),
  requestValidator,
  postServiceOption
);

router.put(
  '/:id',
  checkSchema(updateServiceOption),
  requestValidator,
  putServiceOption
);

router.get('/', getServiceOptionList);

router.patch('/:id', patchChangeStatusServiceOption);

router.delete('/:id', deleteServiceOption);

module.exports = router;
