const sequelize = require('sequelize');
const { ServiceOption, SubService } = require('..');
const {
  successMessage,
  errorMessage,
  serviceStatus,
} = require('../../config/options');
const { Op } = sequelize;
const db = require('../index'); // make sure it exports the sequelize instance

// Check if sub-service exists
const checkIfSubServiceExists = async (subServiceId) => {
  return await SubService.findOne({
    where: { id: subServiceId },
    attributes: ['id'],
  });
};

// Extract names and IDs
const extractOptionMetadata = (options) => {
  const optionNames = options.map((o) => o.name);
  const optionIds = options.map((o) => o.id).filter(Boolean);
  return { optionNames, optionIds };
};

// Check if any service option already exists in DB
const checkDuplicateServiceOption = async (
  subServiceId,
  optionNames,
  excludeIds = []
) => {
  const whereClause = {
    subServiceId,
    name: { [Op.in]: optionNames },
  };

  if (excludeIds.length) {
    whereClause.id = { [Op.notIn]: excludeIds };
  }

  const existingOptions = await ServiceOption.findAll({
    where: whereClause,
    attributes: ['name'],
  });

  return existingOptions.map((opt) => opt.name);
};

const checkAndCreateOrUpdateServiceOptionInBulk = async (
  subServiceId,
  { options = [] }
) => {
  const transaction = await db.sequelize.transaction();
  try {
    // Check if subService exists
    const subService = await checkIfSubServiceExists(subServiceId);
    if (!subService) {
      return {
        success: false,
        message: errorMessage.NO_USER('sub service'),
      };
    }

    const update = [],
      insert = [],
      skipped = [];

    options.forEach((o) => {
      if (o.id)
        update.push({
          ...o,
          subServiceId,
          serviceIntervalType: o.serviceIntervalType,
          serviceIntervalValue: o.serviceIntervalValue,
        });
      else insert.push(o);
    });

    // Check for DB duplicates
    const { optionNames } = extractOptionMetadata(insert);
    const dbDuplicates = await checkDuplicateServiceOption(
      subServiceId,
      optionNames
    );
    const dupSet = new Set(dbDuplicates);

    // Filter insert to remove duplicates
    const finalInsert = insert.reduce((arr, o) => {
      if (dupSet.has(o.name)) {
        skipped.push(o.name);
      } else {
        arr.push({ ...o, subServiceId });
      }
      return arr;
    }, []);

    // Final payload to be created or updated
    const payload = [...update, ...finalInsert];
    if (!payload.length) {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST(
          skipped.length ? skipped.join(', ') : 'All service option names'
        ),
      };
    }

    const data = await ServiceOption.bulkCreate(payload, {
      updateOnDuplicate: ['name'],
      transaction,
    });

    await transaction.commit();

    return {
      success: true,
      message:
        successMessage.SAVED_SUCCESS_MESSAGE('Service options') +
        (skipped.length ? ` (Skipped: ${skipped.join(', ')})` : ''),
      data,
    };
  } catch (e) {
    await transaction.rollback();
    console.error('Error in checkAndCreateOrUpdateServiceOption:', e);
    throw e;
  }
};

const checkANdUpdateSingleServiceOption = async (id, body) => {
  try {
    // Check if the service option exists
    const existingOption = await ServiceOption.findOne({ where: { id } });
    if (!existingOption) {
      return {
        success: false,
        message: errorMessage.DATA_NOT_FOUND('Service option'),
      };
    }

    // Check for duplicate name under the same subServiceId
    const duplicate = await ServiceOption.findOne({
      where: {
        id: { [Op.ne]: id },
        name: body.name,
        subServiceId: body.subServiceId,
      },
    });

    if (duplicate) {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST(
          `Service option name "${body.name}" already exists under the same sub service`
        ),
      };
    }

    // Perform the update
    existingOption.name = body.name;
    existingOption.serviceIntervalType = body.serviceIntervalType;
    existingOption.serviceIntervalValue = body.serviceIntervalValue;
    await existingOption.save();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Service option'),
      data: existingOption,
    };
  } catch (e) {
    console.error('Error updating service option:', e);
    throw e;
  }
};

const getServiceOptionAndCount = async ({
  serviceTypeId,
  subServiceId,
  start = 0,
  limit = 10,
  search = null,
}) => {
  try {
    const where = {
      ...(search && { name: { [Op.iLike]: `%${search}%` } }),
      subServiceId,
    };

    const { count, rows } = await ServiceOption.findAndCountAll({
      where,
      attributes: [
        'id',
        'name',
        'status',
        'serviceIntervalType',
        'serviceIntervalValue',
      ],
      include: [
        {
          model: SubService,
          as: 'subService',
          attributes: ['id', 'name'],
          where: {
            serviceTypeId: serviceTypeId,
          },
        },
      ],
      offset: Number(start),
      limit: Number(limit),
      order: [['createdAt', 'DESC']],
    });

    return {
      message: successMessage.DETAIL_MESSAGE('Service Options'),
      data: {
        rows,
        pagination: {
          totalCount: count,
          start: Number(start),
          limit: Number(limit),
        },
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndUpdateStatus = async (subServiceId, isDeleted = false) => {
  try {
    const existingServiceOption = await ServiceOption.findOne({
      where: {
        id: subServiceId,
      },
      attributes: ['id', 'name', 'status'],
    });

    if (!existingServiceOption) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Service option'),
      };
    }
    if (isDeleted) {
      existingServiceOption.status = serviceStatus.DELETED;
    } else {
      existingServiceOption.status =
        serviceStatus.ACTIVE === existingServiceOption.status
          ? serviceStatus.BLOCKED
          : serviceStatus.ACTIVE;
    }
    await existingServiceOption.save();

    return {
      success: true,
      message: successMessage.CHANGED_SUCCESS_MESSAGE('Service option status'),
      ...(!isDeleted && {
        data: existingServiceOption,
      }),
    };
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndPatchServiceOptionStatus = async (id, isDelete) => {
  try {
    const { success, message } = await checkAndUpdateStatus(id, isDelete);
    return {
      success,
      message,
    };
  } catch (error) {
    throw new Error(error);
  }
};

module.exports = {
  checkAndCreateOrUpdateServiceOptionInBulk,
  checkANdUpdateSingleServiceOption,
  getServiceOptionAndCount,
  checkAndPatchServiceOptionStatus,
};
