const BusinessInfoRepository = require('../../../models/repositories/BusinessInformationRepository');
const { genRes, errorMessage, resCode } = require('../../../config/options');

exports.postBusinessInfo = async (req, res) => {
  try {
    const userId = req.user.id;

    const { success, message, data } =
      await BusinessInfoRepository.checkAndCreateBusinessInfo(userId, req.body);

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.putBusinessInfo = async (req, res) => {
  try {
    const userId = req.user.id;
    const businessInfoId = req.params.id;

    const { success, message, data } =
      await BusinessInfoRepository.checkAndUpdateBusinessInfo(
        userId,
        businessInfoId,
        req.body
      );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.getBusinessInfo = async (req, res) => {
  try {
    const userId = req.user.id;

    const { success, message, data } =
      await BusinessInfoRepository.checkAndGetBusinessInfo(userId);
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }
    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};
