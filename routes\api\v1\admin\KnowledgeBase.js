const { Router } = require('express');
const router = Router();
const {
  postKnowledgeBase,
  getKnowledgeBaseListing,
  patchChangeStatusKnowledgeBase,
  deleteKnowledgeBase,
  putKnowledgeBase,
  getKnowledgeBaseById,
} = require('../../../../controllers/api/v1/admin/KnowledgeBase');
const { checkSchema } = require('express-validator');

const ErrorHandleHelper = require('../../../../models/helpers/ErrorHandleHelper');
const {
  createAndUpdateKnowledgeBase,
} = require('../../../../schema-validation/admin/KnowledgeBase');

router.post(
  '/',
  checkSchema(createAndUpdateKnowledgeBase),
  ErrorHandleHelper.requestValidator,
  postKnowledgeBase
);

router.put(
  '/:id',
  checkSchema(createAndUpdateKnowledgeBase),
  ErrorHandleHelper.requestValidator,
  putKnowledgeBase
);

router.get('/', getKnowledgeBaseListing);

router.patch('/:id', patchChangeStatusKnowledgeBase);

router.delete('/:id', deleteKnowledgeBase);

router.get('/:id', getKnowledgeBaseById);

module.exports = router;
