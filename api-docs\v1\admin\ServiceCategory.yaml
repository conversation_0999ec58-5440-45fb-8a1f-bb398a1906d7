paths:
  /admin/service-category:
    post:
      tags:
        - "ServiceCategory"
      summary: "Create a new Service Category"
      description: "Add a new Service Category with the provided details."
      operationId: "createServiceCategory"
      requestBody:
        description: "Payload containing Service Category details."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/createAndUpdateServiceCategory"
        required: true
      produces:
        - "application/json"
      parameters: []
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Service Category created successfully."
        "400":
          description: "Invalid request payload."
        "500":
          description: "Internal server error."

    get:
      tags:
        - "ServiceCategory"
      summary: "Get list of Service Categories with pagination and search"
      description: "Fetch paginated list of Service Categories with optional search filter."
      operationId: "listServiceCategories"
      parameters:
        - name: "start"
          in: "query"
          description: "Starting index for pagination"
          required: false
          schema:
            type: integer
            default: 0
        - name: "limit"
          in: "query"
          description: "Number of records to return"
          required: false
          schema:
            type: integer
            default: 10
        - name: "search"
          in: "query"
          description: "Search keyword to filter categories by name or status"
          required: false
          schema:
            type: string
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "List of Service Categories fetched successfully."
        "400":
          description: "Invalid request parameters."
        "500":
          description: "Internal server error."

  /admin/service-category/{id}:
    put:
      tags:
        - "ServiceCategory"
      summary: "Update an existing Service Category"
      description: "Update the details of a specific Service Category by ID."
      operationId: "updateServiceCategory"
      parameters:
        - name: id
          in: path
          required: true
          description: "ID of the Service Category to update"
          schema:
            type: string
      requestBody:
        description: "Payload containing updated Service Category details."
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createAndUpdateServiceCategory"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Service Category updated successfully."
        "400":
          description: "Invalid request payload or duplicate name."
        "404":
          description: "Service Category not found."
        "500":
          description: "Internal server error."

    patch:
      tags:
        - "ServiceCategory"
      summary: "Update ServiceCategory status by ID"
      description: "update a ServiceCategory status using its ID"
      operationId: "updateServiceCategoryStatus"
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: "ID of the service category to update status"
          schema:
            type: string
      responses:
        "201":
          description: "Service category status updated successfully"
        "400":
          description: "Invalid service category ID or status change not allowed"
        "404":
          description: "Service category not found"
        "500":
          description: "Internal Server Error"

    delete:
      tags:
        - "ServiceCategory"
      summary: "Delete a Service Category"
      description: "Softdelete a Service category from the system using their ID."
      operationId: "deleteSubCategory"
      produces:
        - "application/json"
      parameters:
        - in: "path"
          name: "id"
          schema:
            type: string
          required: true
          description: "Unique identifier of the Service category."
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Service category deleted successfully."
        "400":
          description: "Invalid Service category by ID."
        "500":
          description: "Internal server error."

components:
  schemas:
    createAndUpdateServiceCategory:
      type: object
      properties:
        name:
          type: string
          description: "Enter the name of Service category"
        imageUrl:
          type: string
          description: "Enter the imageUrl of service category"