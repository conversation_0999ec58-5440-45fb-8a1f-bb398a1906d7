const express = require('express');

const router = express.Router();
const AuthHandler = require('../../../../models/helpers/AuthHelper');

const UserRouter = require('./User');
const SubAdminRouter = require('./SubAdmin');
const ServiceCategoryRouter = require('./ServiceCategory');
const ServiceTypeRouter = require('./ServiceType');
const SubServiceRouter = require('./SubService');
const ServiceOptionRouter = require('./ServiceOption');
const ProviderRouter = require('./Provider');
const ComplianceLegalRouter = require('./ComplianceLegal');
const KnowledgeBaseRouter = require('./KnowledgeBase');
const DealsOnWheelsRouter = require('./DealsOnWheels');
const CustomerRouter = require('./Customer');
const { usersRoles } = require('../../../../config/options');

router.use('/user', UserRouter);

router.use(
  '/sub-admin',
  AuthHandler.authenticateJWT(usersRoles.getAdminArray()),
  SubAdminRouter
);

router.use(
  '/service-category',
  AuthHandler.authenticateJWT(usersRoles.getAdminArray()),
  ServiceCategoryRouter
);

router.use(
  '/service-type',
  AuthHandler.authenticateJWT(usersRoles.getAdminArray()),
  ServiceTypeRouter
);

router.use(
  '/sub-service',
  AuthHandler.authenticateJWT(usersRoles.getAdminArray()),
  SubServiceRouter
);

router.use(
  '/service-option',
  AuthHandler.authenticateJWT(usersRoles.getAdminArray()),
  ServiceOptionRouter
);

router.use(
  '/provider',
  AuthHandler.authenticateJWT(usersRoles.getAdminArray()),
  ProviderRouter
);

router.use(
  '/compliance-legal',
  AuthHandler.authenticateJWT(usersRoles.getAdminArray()),
  ComplianceLegalRouter
);

router.use(
  '/knowledge-base',
  AuthHandler.authenticateJWT(usersRoles.getAdminArray()),
  KnowledgeBaseRouter
);

router.use(
  '/deals-on-wheels',
  AuthHandler.authenticateJWT(usersRoles.getAdminArray()),
  DealsOnWheelsRouter
);

router.use(
  '/customer',
  AuthHandler.authenticateJWT(usersRoles.getAdminArray()),
  CustomerRouter
);

module.exports = router;
