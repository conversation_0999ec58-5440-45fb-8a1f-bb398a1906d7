const sequelize = require('sequelize');
const { SubService, ServiceType, ServiceCategory } = require('..');
const {
  successMessage,
  errorMessage,
  serviceStatus,
} = require('../../config/options');
const { Op, Sequelize } = sequelize;
const serviceOptionRepo = require('./ServiceOptionRepository');

const checkDuplicateSubService = async (body, existingSubservice = null) => {
  try {
    if (!body.name) return null;

    return await SubService.findOne({
      where: {
        name: body.name,
        ...(existingSubservice?.id && {
          id: { [Op.ne]: existingSubservice.id },
        }),
      },
      attributes: ['id', 'name'],
    });
  } catch (error) {
    throw new Error(error);
  }
};

const checkIfServiceTypeExists = async (serviceTypeId) => {
  if (!serviceTypeId) return true;

  const serviceType = await ServiceType.findOne({
    where: { id: serviceTypeId },
    attributes: ['id'],
  });

  return !!serviceType;
};

const checkAndCreateSubService = async (body) => {
  try {
    const serviceTypeExists = await checkIfServiceTypeExists(
      body.serviceTypeId
    );
    if (!serviceTypeExists) {
      return {
        success: false,
        message: errorMessage.NO_USER('Service type'),
      };
    }

    const existingSubservice = await checkDuplicateSubService(body);
    if (existingSubservice) {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST('Sub service name'),
      };
    }

    const { serviceOptions = [], ...subServiceData } = body;

    const subService = await SubService.create(subServiceData);

    let serviceOptionResult = null;
    if (serviceOptions.length > 0) {
      serviceOptionResult =
        await serviceOptionRepo.checkAndCreateOrUpdateServiceOptionInBulk(
          subService.id,
          { options: serviceOptions }
        );
    }

    return {
      success: true,
      message: successMessage.SAVED_SUCCESS_MESSAGE('Sub Service'),
      data: {
        subService,
        ...(serviceOptionResult?.data
          ? { serviceOptions: serviceOptionResult.data }
          : {}),
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndUpdateSubService = async (subServiceId, body) => {
  try {
    const existingSubService = await SubService.findOne({
      where: { id: subServiceId },
    });

    if (!existingSubService) {
      return {
        success: false,
        message: errorMessage.NO_USER('Sub service'),
      };
    }

    const duplicateSubService = await checkDuplicateSubService(
      body,
      existingSubService
    );
    if (duplicateSubService) {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST('Sub service name'),
      };
    }

    Object.assign(existingSubService, body);

    await existingSubService.save();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Sub service'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

const getSubServiceAndCount = async ({
  serviceCategoryId,
  serviceTypeId,
  start = 0,
  limit = 10,
  search = null,
}) => {
  try {
    const where = {
      ...(search && { name: { [Op.iLike]: `%${search}%` } }),
      serviceTypeId,
    };

    const { count, rows } = await SubService.findAndCountAll({
      where,
      attributes: [
        'id',
        'name',
        'status',
        'imageUrl',
        'externalLink',
        [
          Sequelize.literal(`(
            SELECT COUNT(*)
            FROM "ServiceOption" AS so
            WHERE so."subServiceId" = "SubService"."id"
              AND so."status" != 'deleted'
          )`),
          'serviceOptionCount',
        ],
      ],
      include: [
        {
          model: ServiceType,
          as: 'serviceType',
          attributes: ['id', 'name'],
          where: {
            categoryId: serviceCategoryId,
          },
          include: [
            {
              model: ServiceCategory,
              as: 'serviceCategory',
              attributes: ['id', 'name'],
            },
          ],
        },
      ],
      offset: Number(start),
      limit: Number(limit),
      order: [['createdAt', 'DESC']],
    });

    return {
      message: successMessage.DETAIL_MESSAGE('Sub services'),
      data: {
        rows,
        pagination: {
          totalCount: count,
          start: Number(start),
          limit: Number(limit),
        },
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndUpdateStatus = async (subServiceId, isDeleted = false) => {
  try {
    const existingSubService = await SubService.findOne({
      where: {
        id: subServiceId,
      },
      attributes: ['id', 'name', 'status'],
    });

    if (!existingSubService) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Sub service'),
      };
    }
    if (isDeleted) {
      existingSubService.status = serviceStatus.DELETED;
    } else {
      existingSubService.status =
        serviceStatus.ACTIVE === existingSubService.status
          ? serviceStatus.BLOCKED
          : serviceStatus.ACTIVE;
    }
    await existingSubService.save();

    return {
      success: true,
      message: successMessage.CHANGED_SUCCESS_MESSAGE('Sub service status'),
      ...(!isDeleted && {
        data: existingSubService,
      }),
    };
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndPatchSubServiceStatus = async (id, isDelete) => {
  try {
    const { success, message } = await checkAndUpdateStatus(id, isDelete);
    return {
      success,
      message,
    };
  } catch (error) {
    throw new Error(error);
  }
};

module.exports = {
  checkAndCreateSubService,
  checkAndUpdateSubService,
  getSubServiceAndCount,
  checkAndPatchSubServiceStatus,
};
