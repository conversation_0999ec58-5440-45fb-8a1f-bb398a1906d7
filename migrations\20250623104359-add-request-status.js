'use strict';

const { serviceRequestStatus } = require('../config/options');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('ServiceRequestProvider', 'requestStatus', {
      type: Sequelize.STRING,
      allowNull: false,
      defaultValue: serviceRequestStatus.AVAILABLE,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn(
      'ServiceRequestProvider',
      'requestStatus'
    );
  },
};
