const UserRepository = require('../../../models/repositories/UserRepository');

const {
  genRes,
  errorMessage,
  resCode,
  errorTypes,
} = require('../../../config/options');
const {
  checkGoogleToken,
  checkAppleToken,
  checkFacebookToken,
} = require('../../../models/helpers/SingleSSOHelper');

exports.sendLoginOtp = async (req, res) => {
  try {
    const { success, message } = await UserRepository.checkAndSendOTPForLogin(
      req.body
    );
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(
            resCode.HTTP_BAD_REQUEST,
            message,
            errorTypes.ACCESS_DENIED_EXCEPTION
          )
        );
    }
    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.verifyLoginOtp = async (req, res) => {
  try {
    const { success, message, data } = await UserRepository.checkAndVerifyOtp(
      req.body
    );
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(resCode.HTTP_BAD_REQUEST, message, errorTypes.INPUT_VALIDATION)
        );
    }
    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.sendOtpToMobileAndEmailChange = async (req, res) => {
  try {
    const { success, message } =
      await UserRepository.checkAndSendOTPForChangeEmailAndMobile(
        req.body,
        req.user.id
      );
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(
            resCode.HTTP_BAD_REQUEST,
            message,
            errorTypes.ACCESS_DENIED_EXCEPTION
          )
        );
    }
    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.verifyOtpForEmailAndMobileChange = async (req, res) => {
  try {
    const { success, message, data } =
      await UserRepository.checkAndVerifyOtpToChangeEmailAndMobile(
        req.body,
        req.user.id
      );
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(resCode.HTTP_BAD_REQUEST, message, errorTypes.INPUT_VALIDATION)
        );
    }
    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.putUserProfile = async (req, res) => {
  try {
    const { id } = req.user;
    const query = {
      where: {
        id,
      },
      attributes: {
        exclude: ['tempOtp', 'tempOtpExpiresAt', 'password'],
      },
    };
    const { success, message, data } = await UserRepository.checkAndUpdateUser(
      query,
      req.body
    );
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }
    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.getUserProfile = async (req, res) => {
  try {
    const { success, message, data } = await UserRepository.getUserProfile(
      req.user.id
    );
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }
    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.deleteUserAccount = async (req, res) => {
  try {
    const userId = req.user.id;

    const { success, message } = await UserRepository.checkAndUpdateStatus(
      userId,
      true
    );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res
      .status(resCode.HTTP_CREATE)
      .json(genRes(resCode.HTTP_CREATE, { message }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.signupPhone = async (req, res) => {
  try {
    const { success, data, message } = await UserRepository.signupWithPhone(
      req.body
    );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { data, message }));
  } catch (e) {
    console.error(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.verifySignPhone = async (req, res) => {
  try {
    const { success, data, message } = await UserRepository.checkAndVerifyOtp(
      req.body,
      true
    );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { data, message }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.loginWitSSO = async (req, res) => {
  try {
    let payload = {
      firstName: req.body.firstName,
      lastName: req.body.lastName,
      role: req.body.role,
    };
    switch (req.body.ssoProvider) {
      case 'google':
        const googleResp = await checkGoogleToken(req.body);
        if (!googleResp.success) {
          return res
            .status(resCode.HTTP_BAD_REQUEST)
            .json(
              genRes(
                resCode.HTTP_BAD_REQUEST,
                googleResp.message,
                errorTypes.OAUTH_EXCEPTION
              )
            );
        }
        payload = {
          ...payload,
          googleId: req.body.googleId,
          email: googleResp.data.email.toLowerCase(),
        };
        break;
      case 'apple':
        const appleResp = await checkAppleToken(req.body);
        if (!appleResp.success) {
          return res
            .status(resCode.HTTP_BAD_REQUEST)
            .json(
              genRes(
                resCode.HTTP_BAD_REQUEST,
                appleResp.message,
                errorTypes.OAUTH_EXCEPTION
              )
            );
        }
        payload = {
          ...payload,
          appleId: req.body.appleId,
          email: appleResp.email.toLowerCase(),
        };
        break;
      case 'facebook':
        const facebookResp = await checkFacebookToken(req.body);
        if (!facebookResp.success) {
          return res
            .status(resCode.HTTP_BAD_REQUEST)
            .json(
              genRes(
                resCode.HTTP_BAD_REQUEST,
                facebookResp.message,
                errorTypes.OAUTH_EXCEPTION
              )
            );
        }
        payload = {
          ...payload,
          facebookId: req.body.facebookId,
          email: facebookResp.data.email.toLowerCase(),
        };
        break;
      default:
        break;
    }
    if (!payload) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(
            resCode.HTTP_BAD_REQUEST,
            errorMessage.INVALID_REQUEST,
            errorTypes.OAUTH_EXCEPTION
          )
        );
    }
    const { success, data, message } =
      await UserRepository.checkAndCreate(payload);
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }
    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};
