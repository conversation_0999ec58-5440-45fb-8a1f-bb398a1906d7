'use strict';

const { serviceInterval } = require('../config/options');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('ServiceOption', 'serviceIntervalType', {
      type: Sequelize.STRING,
      allowNull: false,
      defaultValue: serviceInterval.MILES,
    });

    await queryInterface.addColumn('ServiceOption', 'serviceIntervalValue', {
      type: Sequelize.STRING,
      allowNull: true,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('ServiceOption', 'serviceIntervalType');
    await queryInterface.removeColumn('ServiceOption', 'serviceIntervalValue');
  },
};
