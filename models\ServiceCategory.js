'use strict';

const { serviceStatus, generateCloudFrontUrl } = require('../config/options');

const { Op } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  const ServiceCategory = sequelize.define(
    'ServiceCategory',
    {
      id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      imageUrl: {
        allowNull: true,
        type: DataTypes.TEXT,
        get() {
          return generateCloudFrontUrl(this.getDataValue('imageUrl'));
        },
        set(file) {
          if (file) {
            this.setDataValue(
              'imageUrl',
              `uploads/${file.split('uploads/')[1]}`
            );
          }
        },
      },
      status: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: serviceStatus.ACTIVE,
      },
    },
    {
      tableName: 'ServiceCategory',
      timestamps: true,
      defaultScope: {
        where: {
          status: {
            [Op.ne]: serviceStatus.DELETED,
          },
        },
      },

      scopes: {
        withDeleted: {
          // No conditions here to include all records including deleted
        },
        onlyDeleted: {
          where: {
            status: serviceStatus.DELETED,
          },
        },
      },
    }
  );

  ServiceCategory.associate = function (models) {
    ServiceCategory.belongsTo(models.Service, {
      foreignKey: 'serviceId',
      as: 'service',
    });
    ServiceCategory.hasMany(models.ServiceType, {
      foreignKey: 'categoryId',
      as: 'serviceTypes',
      onDelete: 'CASCADE',
    });
  };

  return ServiceCategory;
};
