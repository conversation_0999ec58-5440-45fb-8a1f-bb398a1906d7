const ReviewRatingRepository = require('../../../models/repositories/ReviewRatingRepository');
const { genRes, errorMessage, resCode } = require('../../../config/options');

exports.postReviewRating = async (req, res) => {
  try {
    const userId = req.user.id;

    const { success, message, data } =
      await ReviewRatingRepository.checkAndCreateReviewRating(userId, req.body);

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.getReviewRatingListing = async (req, res) => {
  try {
    const userId = req.user.id;
    const { start = 0, limit = 10 } = req.query;

    const { message, data } = await ReviewRatingRepository.getReviewRatingCount(
      {
        userId,
        start: parseInt(start),
        limit: parseInt(limit),
      }
    );

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.getReviewRatingStats = async (req, res) => {
  try {
    const userId = req.user.id;

    const { message, data } =
      await ReviewRatingRepository.getReviewRatingStats(userId);

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};
