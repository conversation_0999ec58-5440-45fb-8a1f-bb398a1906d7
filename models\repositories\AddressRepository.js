const { Address } = require('..');
const { successMessage, errorMessage } = require('../../config/options');
const {
  getPlacesAutoComplete,
  getCoordinatesByZipcode,
} = require('../helpers/GoogleHelper');

const parseAddressComponents = (geocodeResult) => {
  const components = geocodeResult.results?.[0]?.address_components || [];
  const formattedAddress = geocodeResult.results?.[0]?.formatted_address || '';
  const geometry = geocodeResult.results?.[0]?.geometry || {};
  let city = '';
  let state = '';
  let postalCode = '';

  components.forEach((component) => {
    if (component.types.includes('locality')) {
      city = component.long_name;
    }
    if (component.types.includes('administrative_area_level_1')) {
      state = component.short_name;
    }
    if (component.types.includes('postal_code')) {
      postalCode = component.long_name;
    }
  });

  const addressLine1 = `${city}, ${state} ${postalCode}`;

  return {
    city,
    state,
    addressLine1,
    fullAddress: formattedAddress,
    geometry,
  };
};

const createAddress = async (userId, body) => {
  try {
    let {
      zipcode,
      latitude,
      longitude,
      radius,
      addressLine1 = '',
      city = '',
      state = '',
      category = 'home',
      addressType,
    } = body;

    let location = null;
    let parsed = null;

    const hasCoordinates = latitude && longitude;

    if (hasCoordinates) {
      location = {
        type: 'Point',
        coordinates: [parseFloat(longitude), parseFloat(latitude)],
      };
    } else {
      const response = await getCoordinatesByZipcode(zipcode);
      if (response.status !== 'OK') {
        return {
          success: false,
          message: errorMessage.INCORRECT_DATA('ZIP code'),
          data: null,
        };
      }

      parsed = parseAddressComponents(response);

      // Get coordinates from response
      location = {
        type: 'Point',
        coordinates: [
          parsed.geometry.location.lng,
          parsed.geometry.location.lat,
        ],
      };
    }
    // Fill in any missing address details
    if (parsed) {
      city = city || parsed.city;
      state = state || parsed.state;
      addressLine1 = addressLine1 || parsed.addressLine1;
    }
    const address = await Address.create({
      userId,
      zipcode,
      location,
      radius,
      addressLine1,
      city,
      state,
      category,
      addressType,
    });

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('ZIP code'),
      data: address,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || 'Something went wrong',
      data: null,
    };
  }
};

const searchPlaces = async ({ search, mode, latitude, longitude }) => {
  try {
    let data = [];
    switch (mode) {
      case 'place': {
        let response = await getPlacesAutoComplete({
          query: search,
          center: { latitude, longitude },
        });
        data = response.suggestions.map((item) => ({
          id: item.placePrediction.placeId,
          name: item.placePrediction.text.text,
        }));
        break;
      }
      default:
        data = [];
    }
    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE('Places'),
      data,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

const getAddressAndCount = async ({ userId, start = 0, limit = 10 }) => {
  try {
    const where = {
      userId,
    };
    const { count, rows } = await Address.findAndCountAll({
      where,
      offset: Number(start),
      limit: Number(limit),
      order: [['createdAt', 'DESC']],
      attributes: [
        'id',
        'zipcode',
        'city',
        'state',
        'addressType',
        'addressLine1',
      ],
    });

    return {
      message: successMessage.DETAIL_MESSAGE('Address'),
      data: {
        rows,
        pagination: {
          totalCount: count,
          start: Number(start),
          limit: Number(limit),
        },
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

module.exports = {
  createAddress,
  searchPlaces,
  getAddressAndCount,
};
