const express = require('express');

const router = express.Router();
const AuthHandler = require('../../../models/helpers/AuthHelper');
const { usersRoles } = require('../../../config/options');

const User = require('./User');
const SharedRouter = require('./Shared');
const AdminRouter = require('./admin/index');
const AddressRouter = require('./Address');
const VehicleRouter = require('./Vehicle');
const ServiceRouter = require('./Service');
const ServiceCategoryRouter = require('./ServiceCategory');
const ServiceTypeRouter = require('./ServiceType');
const SubServiceRouter = require('./SubService');
const ServiceOptionRouter = require('./ServiceOption');
const BusinessInformationRouter = require('./BusinessInformation');
const ProvidedServiceRouter = require('./ProvidedService');
const ServiceRequestRouter = require('./ServiceRequest');
const ProviderRouter = require('./Provider');
const ReviewRatingRouter = require('./ReviewRating');
const KnowledgeBaseRouter = require('./KnowledgeBase');
const ChatbotRouter = require('./Chatbot');

router.use('/user', User);
router.use('/shared', SharedRouter);
router.use('/admin', AdminRouter);
router.use(
  '/address',
  AuthHandler.authenticateJWT([usersRoles.CUSTOMER, usersRoles.PROVIDER]),
  AddressRouter
);
router.use(
  '/vehicle',
  AuthHandler.authenticateJWT([usersRoles.CUSTOMER]),
  VehicleRouter
);

router.use('/service', ServiceRouter);

router.use(
  '/service-category',
  AuthHandler.authenticateJWT([usersRoles.CUSTOMER, usersRoles.PROVIDER]),
  ServiceCategoryRouter
);

router.use(
  '/service-type',
  AuthHandler.authenticateJWT([usersRoles.CUSTOMER, usersRoles.PROVIDER]),
  ServiceTypeRouter
);

router.use(
  '/sub-service',
  AuthHandler.authenticateJWT([usersRoles.CUSTOMER]),
  SubServiceRouter
);

router.use(
  '/service-option',
  AuthHandler.authenticateJWT([usersRoles.CUSTOMER]),
  ServiceOptionRouter
);

router.use(
  '/business-info',
  AuthHandler.authenticateJWT([usersRoles.PROVIDER]),
  BusinessInformationRouter
);

router.use(
  '/provided-service',
  AuthHandler.authenticateJWT([usersRoles.PROVIDER]),
  ProvidedServiceRouter
);

router.use('/service-request', ServiceRequestRouter);

router.use(
  '/provider',
  AuthHandler.authenticateJWT([usersRoles.CUSTOMER, usersRoles.PROVIDER]),
  ProviderRouter
);

router.use(
  '/review',
  AuthHandler.authenticateJWT([usersRoles.CUSTOMER, usersRoles.PROVIDER]),
  ReviewRatingRouter
);

router.use('/knowledge-base', KnowledgeBaseRouter);
router.use('/chatbot', ChatbotRouter);

module.exports = router;
