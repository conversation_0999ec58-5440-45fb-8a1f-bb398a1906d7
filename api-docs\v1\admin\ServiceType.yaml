paths:
  /admin/service-type:
    post:
      tags:
        - "ServiceType"
      summary: "Create a new Service Type"
      description: "Add a new Service Type with the provided details."
      operationId: "createServiceType"
      requestBody:
        description: "Payload containing Service Type details."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/createAndUpdateServiceType"
        required: true
      produces:
        - "application/json"
      parameters: []
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Service type created successfully."
        "400":
          description: "Invalid request payload."
        "500":
          description: "Internal server error."

    get:
      tags:
        - "ServiceType"
      summary: "Get list of Service Type with pagination and search"
      description: "Fetch paginated list of Service Type with optional search filter."
      operationId: "listServiceTypes"
      parameters:
        - name: "categoryId"
          in: "query"
          required: true
          description: "Service Category ID"
          schema:
            type: string
            format: uuid
        - name: "start"
          in: "query"
          description: "Starting index for pagination"
          required: false
          schema:
            type: integer
            default: 0
        - name: "limit"
          in: "query"
          description: "Number of records to return"
          required: false
          schema:
            type: integer
            default: 10
        - name: "search"
          in: "query"
          description: "Search keyword to filter types by name"
          required: false
          schema:
            type: string
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "List of Service Types fetched successfully."
        "400":
          description: "Invalid request parameters."
        "500":
          description: "Internal server error."

  /admin/service-type/{id}:
    put:
      tags:
        - "ServiceType"
      summary: "Update an existing Service Type"
      description: "Update the details of a specific Service Type by ID."
      operationId: "updateServiceType"
      parameters:
        - name: id
          in: path
          required: true
          description: "ID of the Service Type to update"
          schema:
            type: string
      requestBody:
        description: "Payload containing updated Service Category details."
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createAndUpdateServiceType"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Service Type updated successfully."
        "400":
          description: "Invalid request payload or duplicate name."
        "404":
          description: "Service Type not found."
        "500":
          description: "Internal server error."

    patch:
      tags:
        - "ServiceType"
      summary: "Update ServiceType status by ID"
      description: "update a ServiceType status using its ID"
      operationId: "updateServiceTypeStatus"
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: "ID of the service type to update status"
          schema:
            type: string
      responses:
        "201":
          description: "Service type status updated successfully"
        "400":
          description: "Invalid service type ID or status change not allowed"
        "404":
          description: "Service type not found"
        "500":
          description: "Internal Server Error"

    delete:
      tags:
        - "ServiceType"
      summary: "Delete a Service Type"
      description: "Softdelete a Service type from the system using their ID."
      operationId: "deleteservicetype"
      produces:
        - "application/json"
      parameters:
        - in: "path"
          name: "id"
          schema:
            type: string
          required: true
          description: "Unique identifier of the Service type."
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Service type deleted successfully."
        "400":
          description: "Invalid Service type by ID."
        "500":
          description: "Internal server error."

components:
  schemas:
    createAndUpdateServiceType:
      type: object
      properties:
        categoryId:
          type: string
          description: "Enter the categoryId of Service category"
        name:
          type: string
          description: "Enter the name of Service type"
        description:
          type: string
          description: "Enter the description of Service type"
        imageUrl:
          type: string
          description: "Enter the imageUrl of service type"