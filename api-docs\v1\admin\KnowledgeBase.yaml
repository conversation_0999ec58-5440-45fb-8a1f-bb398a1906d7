paths:
  /admin/knowledge-base:
    post:
      tags:
        - "KnowledgeBase"
      summary: "Create a new KnowledgeBase"
      description: "Add a new Frequently Asked Question with title, description, and optional thumbnail."
      operationId: "createKnowledgeBase"
      requestBody:
        description: "Payload containing KnowledgeBase details."
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createAndUpdateKnowledgeBase"
      produces:
        - application/json
      parameters: []
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Knowledge Base created successfully."
        "400":
          description: "Invalid request payload."
        "500":
          description: "Internal server error."

    get:
      tags:
        - "KnowledgeBase"
      summary: "Get a list of Knowledge Base entries"
      description: "Fetch a paginated list of Knowledge Base entries created by the logged-in admin. Supports search and pagination."
      operationId: "getKnowledgeBaseList"
      parameters:
        - name: start
          in: query
          required: false
          schema:
            type: integer
            default: 0
          description: "Pagination offset (default: 0)"
        - name: limit
          in: query
          required: false
          schema:
            type: integer
            default: 10
          description: "Number of entries to return (default: 10)"
        - name: search
          in: query
          required: false
          schema:
            type: string
          description: "Search by title (case-insensitive partial match)"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "List of Knowledge Base entries fetched successfully."
        "500":
          description: "Internal server error."

  /admin/knowledge-base/{id}:
    patch:
      tags:
        - "KnowledgeBase"
      summary: "Update KnowledgeBase status by ID"
      description: "update a KnowledgeBase status using its ID"
      operationId: "updateKnowledgeBaseStatus"
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: "ID of the  KnowledgeBase to update status"
          schema:
            type: string
      responses:
        "201":
          description: "KnowledgeBase status updated successfully"
        "400":
          description: "Invalid Knowledge Base ID or status change not allowed"
        "404":
          description: "Knowledge Base not found"
        "500":
          description: "Internal Server Error"

    delete:
      tags:
        - "KnowledgeBase"
      summary: "Delete a KnowledgeBase"
      description: "Softdelete a KnowledgeBase from the system using their ID."
      operationId: "deleteKnowledgeBase"
      produces:
        - "application/json"
      parameters:
        - in: "path"
          name: "id"
          schema:
            type: string
          required: true
          description: "Unique identifier of the KnowledgeBase"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "KnowledgeBase deleted successfully."
        "400":
          description: "Invalid KnowledgeBase by ID."
        "500":
          description: "Internal server error."

    put:
      tags:
        - "KnowledgeBase"
      summary: "Update an existing Knowledge base"
      description: "update an existing Knowledge base by ID. Requires a bearer token."
      operationId: "updateKnowledgeBase"
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the Knowledge Base to update
          schema:
            type: integer
      requestBody:
        description: "Payload with updated Knowledge base details"
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createAndUpdateKnowledgeBase"
      produces:
        - "application/json"
      responses:
        "200":
          description: "Knowledge base updated successfully"
        "400":
          description: "Invalid input or Knowledge base not found"
        "500":
          description: "Internal Server Error"

    get:
      tags:
        - "KnowledgeBase"
      summary: "Get Knowledge base entry by ID"
      description: "Retrieve details of a specific Knowledge base record by its ID."
      operationId: "getKnowledgeBaseById"
      produces:
        - "application/json"
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: "ID of the Knowledge base entry"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Knowledge base details retrieved successfully."
        "404":
          description: "Knowledge base entry not found."
        "500":
          description: "Internal server error."

components:
  schemas:
    createAndUpdateKnowledgeBase:
      type: object
      properties:
        title:
          type: string
          example: "How to reset my password?"
        description:
          type: string
          example: "To reset your password, go to the login page and click 'Forgot Password'."
        thumbnail:
          type: string
          format: uri
          example: "https://example.com/image.png"
        status:
          type: string
          enum: [draft, active]
          example: draft
        userType:
          type: string
          enum: [PROVIDER, CUSTOMER]
          default: CUSTOMER
      required:
        - title
        - description
