const {
  usersRoles,
  serviceAppointmentUpdateType,
  serviceRequestStatus,
} = require('../../config/options');

exports.patchAppointmentByType = (payload) => {
  const {
    serviceRequest,
    operation,
    cancellationReason,
    rescheduleReason,
    scheduledAt,
    loggedUserId,
    currentTime,
    userRole,
  } = payload;

  switch (operation) {
    case serviceAppointmentUpdateType.CONFIRM:
      serviceRequest.status = serviceRequestStatus.CONFIRMED;
      serviceRequest.providerId = loggedUserId;
      break;
    case serviceAppointmentUpdateType.START:
      serviceRequest.status = serviceRequestStatus.STARTED;
      serviceRequest.serviceStartedAt = currentTime;
      break;
    case serviceAppointmentUpdateType.CLOSE:
      serviceRequest.status = serviceRequestStatus.COMPLETED;
      serviceRequest.serviceCompletedAt = currentTime;
      break;
    case serviceAppointmentUpdateType.CANCEL:
      serviceRequest.cancellationReason = cancellationReason;
      serviceRequest.cancelledById = loggedUserId;
      serviceRequest.status = serviceRequestStatus.CANCELLED;
      break;
    case serviceAppointmentUpdateType.RESCHEDULE:
      serviceRequest.status =
        userRole === usersRoles.CUSTOMER
          ? serviceRequestStatus.PENDING
          : serviceRequestStatus.CONFIRMED;
      serviceRequest.rescheduleReason = rescheduleReason;
      serviceRequest.rescheduledById = loggedUserId;
      serviceRequest.scheduledAt = scheduledAt;
      break;
    default:
      throw new Error('Invalid type for appointment update.');
  }
};
