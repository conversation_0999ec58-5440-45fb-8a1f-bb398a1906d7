{"name": "revilo_core_service", "version": "1.0.0", "description": "Backend api service", "main": "app.js", "license": "MIT", "type": "commonjs", "scripts": {"start": "npm run migrate:up && node app", "lint": "eslint config/ controllers/ models/ routes/ schema-validation/ app.js --fix --cache", "prettier": "prettier --config ./.prettierrc \"*/**/*{.js,.json,.html}\" --write", "format": "npm run prettier && npm run lint", "migrate:up": "npx sequelize-cli db:migrate", "migrate:undo": "npx sequelize-cli db:migrate:undo", "seed:up": "npx sequelize-cli db:seed:all", "seed:undo": "npx sequelize-cli db:seed:undo", "prepare": "test -d .git && husky install || echo 'Skipping husky install in production'"}, "husky": {"hooks": {"pre-commit": "pnpm run format"}}, "dependencies": {"@aws-sdk/client-s3": "^3.717.0", "@aws-sdk/credential-provider-node": "^3.716.0", "@aws-sdk/s3-request-presigner": "^3.717.0", "@langchain/core": "^0.3.62", "@langchain/google-genai": "^0.2.14", "apple-signin-auth": "^2.0.0", "awesome-phonenumber": "^7.4.0", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "chalk": "4.1.2", "compression": "^1.7.5", "cors": "^2.8.5", "dotenv": "^16.5.0", "ejs": "^3.1.10", "errorhandler": "^1.5.1", "exceljs": "^4.4.0", "express": "^5.1.0", "express-basic-auth": "^1.2.1", "express-useragent": "^1.0.15", "express-validator": "^7.2.1", "jsonwebtoken": "^9.0.2", "langchain": "^0.3.29", "lodash": "^4.17.21", "lusca": "^1.7.0", "luxon": "^3.6.1", "mime": "^4.0.6", "morgan": "^1.10.0", "morgan-body": "^2.6.9", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "nanoid": "^5.0.9", "nodemailer": "^6.10.1", "pdfmake": "^0.2.19", "pg": "^8.15.6", "pug": "^3.0.3", "sequelize": "^6.37.5", "sequelize-cli": "6.6.2", "shortid": "^2.2.16", "swagger-jsdoc": "^6.2.8", "swagger-ui": "^5.21.0", "swagger-ui-express": "^5.0.1", "twilio": "^5.7.0", "xlsx": "^0.18.5"}, "devDependencies": {"eslint": "^9.17.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.1", "husky": "^9.1.7", "prettier": "^3.4.2", "prettier-eslint": "^16.3.0"}, "resolutions": {"chalk": "4.1.2"}, "engines": {"node": ">=20.12.1"}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39"}