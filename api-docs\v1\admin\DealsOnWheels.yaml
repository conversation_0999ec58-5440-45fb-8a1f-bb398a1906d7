paths:
  /admin/deals-on-wheels:
    post:
      tags:
        - "DealsOnWheels"
      summary: "Create a new Deals On Wheels entry"
      description: "Add a new Deals On Wheels entry with poster image and redirection URL."
      operationId: "createDealsOnWheels"
      requestBody:
        description: "Payload containing Deals On Wheels details."
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createDealsOnWheels"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Deals On Wheels entry created successfully."
        "400":
          description: "Invalid request payload."
        "500":
          description: "Internal server error."

    get:
      tags:
        - "DealsOnWheels"
      summary: "Get a list of Deals On Wheels entries"
      description: "Fetch a paginated list of Deals On Wheels entries."
      operationId: "getDealsOnWheelsList"
      parameters:
        - name: start
          in: query
          required: false
          schema:
            type: integer
            default: 0
          description: "Pagination offset (default: 0)"
        - name: limit
          in: query
          required: false
          schema:
            type: integer
            default: 10
          description: "Number of entries to return (default: 10)"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "List of Deals On Wheels entries fetched successfully."
        "500":
          description: "Internal server error."

  /admin/deals-on-wheels/{id}:
    put:
      tags:
        - "DealsOnWheels"
      summary: "Update a Deals On Wheels entry"
      description: "Update poster image, redirection URL, or status of a Deals On Wheels entry."
      operationId: "updateDealsOnWheels"
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: "ID of the Deals On Wheels entry to update"
      requestBody:
        description: "Payload to update Deals On Wheels entry."
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/updateDealsOnWheels"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Deals On Wheels entry updated successfully."
        "400":
          description: "Invalid request payload."
        "404":
          description: "Deals On Wheels entry not found."
        "500":
          description: "Internal server error."

    patch:
      tags:
        - "DealsOnWheels"
      summary: "Update status of a Deals On Wheels entry"
      description: "Update only the status of a Deals On Wheels entry."
      operationId: "updateDealsOnWheelsStatus"
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: "ID of the Deals On Wheels entry to update status"
      requestBody:
        description: "Payload to update status of Deals On Wheels entry."
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/updateDealsOnWheelsStatus"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Deals On Wheels status updated successfully."
        "400":
          description: "Invalid request payload."
        "404":
          description: "Deals On Wheels entry not found."
        "500":
          description: "Internal server error."

components:
  schemas:
    createDealsOnWheels:
      type: object
      properties:
        posterImage:
          type: string
          example: "https://example.com/image.png"
        redirectionUrl:
          type: string
          example: "https://example.com/redirect"
      required:
        - posterImage
        - redirectionUrl

    updateDealsOnWheels:
      type: object
      properties:
        posterImage:
          type: string
          example: "https://example.com/image.png"
        redirectionUrl:
          type: string
          example: "https://example.com/redirect"

    updateDealsOnWheelsStatus:
      type: object
      properties:
        status:
          type: string
          enum: [active, inactive, deleted]
          example: "active"