const { Router } = require('express');

const router = Router();
const { checkSchema } = require('express-validator');

const {
  getProviderListing,
  getProviderById,
  patchChangeStatusProvider,
  deleteProvider,
} = require('../../../../controllers/api/v1/admin/Provider');

const {
  requestValidator,
} = require('../../../../models/helpers/ErrorHandleHelper');
const {
  providerDateQueryValidation,
} = require('../../../../schema-validation/admin/Provider');

router.get(
  '/',
  checkSchema(providerDateQueryValidation),
  requestValidator,
  getProviderListing
);

router.get('/:id', getProviderById);

router.patch('/:id', patchChangeStatusProvider);

router.delete('/:id', deleteProvider);

module.exports = router;
