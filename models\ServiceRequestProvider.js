const { serviceRequestProviderStatus } = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const ServiceRequestProvider = sequelize.define(
    'ServiceRequestProvider',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      requestStatus: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: serviceRequestProviderStatus.PENDING,
      },
      actionAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
    },
    {
      tableName: 'ServiceRequestProvider',
      timestamps: true,
    }
  );

  ServiceRequestProvider.associate = function (models) {
    ServiceRequestProvider.belongsTo(models.ServiceRequest, {
      foreignKey: 'requestId',
      as: 'serviceRequest',
      onDelete: 'CASCADE',
    });

    ServiceRequestProvider.belongsTo(models.User, {
      foreignKey: 'providerId',
      as: 'provider',
      onDelete: 'CASCADE',
    });
  };

  return ServiceRequestProvider;
};
