paths:
  /business-info/document:
    post:
      tags:
        - "BusinessDocument"
      summary: "Upload Business Documents"
      description: "Allows a logged-in user to upload one or multiple business documents"
      operationId: "uploadBusinessDocuments"
      security:
        - bearerAuth: []
      requestBody:
        description: "Payload containing document file metadata"
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/addBusinessDocuments"
      produces:
        - "application/json"
      responses:
        "201":
          description: "Business documents uploaded successfully"
        "400":
          description: "Invalid input"
        "500":
          description: "Internal Server Error"

    get:
      tags:
        - "BusinessDocument"
      summary: "Get Business Document details by ID"
      description: "Fetch business Documents using business Id"
      operationId: "getBusinessDocument"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Business Documents retrieved successfully"
        "400":
          description: "Invalid Business Document ID"
        "404":
          description: "Business Document not found"
        "500":
          description: "Internal Server Error"

  /business-info/document/data:
    put:
      tags:
        - "BusinessDocument"
      summary: Update Business Documents
      description: Allows a logged-in user to update one or multiple business documents using their IDs
      operationId: updateBusinessDocuments
      security:
        - bearerAuth: []
      requestBody:
        description: Payload containing updated document metadata with IDs
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/updateBusinessDocuments'
      produces:
        - application/json
      responses:
        '200':
          description: Business documents updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Business Documents updated successfully
        '400':
          description: Invalid input
        '404':
          description: Document not found or does not belong to the user
        '500':
          description: Internal Server Error

components:
  schemas:
    addBusinessDocuments:
      type: object
      required:
        - documents
      properties:
        documents:
          type: array
          description: "Array of documents to upload"
          items:
            type: object
            required:
              - fileName
              - fileSize
              - fileType
              - filePath
            properties:
              fileName:
                type: string
                example: "pan_card.pdf"
              fileSize:
                type: integer
                example: 102400
              fileType:
                type: string
                example: "application/pdf"
              fileCategory:
                type: string
                example: "PAN"
              filePath:
                type: string
                example: "uploads/user123/pan_card.pdf"

    updateBusinessDocuments:
      type: object
      required:
        - documents
      properties:
        documents:
          type: array
          items:
            type: object
            required:
              - id
            properties:
              id:
                type: integer
                example: 1
              fileName:
                type: string
                example: updated_pan_card.pdf
              fileSize:
                type: integer
                example: 1048576
              fileType:
                type: string
                enum: [image, video, pdf]
                example: pdf
              fileCategory:
                type: string
                enum: [legal, financial, personal]
                example: financial
              filePath:
                type: string
                example: /uploads/documents/updated_pan_card.pdf