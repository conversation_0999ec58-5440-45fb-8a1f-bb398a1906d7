const { Service } = require('..');
const { successMessage } = require('../../config/options');

const getServices = async () => {
  try {
    const data = await Service.findAll({
      order: [['id', 'ASC']],
      attributes: ['id', 'name', 'imageUrl', 'bannerUrl', 'status'],
    });

    return {
      message: successMessage.DETAIL_MESSAGE('Services'),
      data,
    };
  } catch (error) {
    throw new Error(error);
  }
};

module.exports = {
  getServices,
};
