paths:
  /admin/customer:
    get:
      tags:
        - "Customer"
      summary: "Get All Customers"
      description: "Retrieve a list of all customers with their names, emails, mobile numbers, and status."
      operationId: "getAllCustomers"
      security:
        - bearerAuth: []
      parameters:
        - name: start
          in: query
          required: false
          schema:
            type: integer
            default: 0
          description: Pagination start index (default is 0).
        - name: limit
          in: query
          required: false
          schema:
            type: integer
            default: 10
          description: Number of records to retrieve (default is 10).
        - name: search
          in: query
          required: false
          schema:
            type: string
          description: >
            Search keyword to filter customers by email or mobile number.
        - name: fromDate
          in: query
          required: false
          schema:
            type: string
            example: "2024-01-01"
          description: >
            Filter providers created on or after this date (format: YYYY-MM-DD).
        - name: toDate
          in: query
          required: false
          schema:
            type: string
            example: "2024-12-31"
          description: >
            Filter providers created on or before this date (format: YYYY-MM-DD).
      responses:
        "200":
          description: "Customers retrieved successfully"
        "400":
          description: "Invalid query parameters"
        "500":
          description: "Internal Server Error"

  /admin/customer/{id}:
    get:
      summary: Get customer details by ID
      description: "Get Customer List By its Id."
      operationId: "getCustomerById"
      security:
        - bearerAuth: []
      tags:
        - "Customer"
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: The unique ID of the Customer
      responses:
        '200':
          description: Customer details retrieved successfully
        '404':
          description: Customer not found

    patch:
      tags:
        - "Customer"
      summary: "Update Customer status by ID"
      description: "update a Customer status using its ID"
      operationId: "updateCustomerStatus"
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: "ID of the  Customer to update status"
          schema:
            type: string
      responses:
        "201":
          description: "Customer status updated successfully"
        "400":
          description: "Invalid customer ID or status change not allowed"
        "404":
          description: "Customer not found"
        "500":
          description: "Internal Server Error"

    delete:
      tags:
        - "Customer"
      summary: "Delete a Customer"
      description: "Softdelete a Customer from the system using their ID."
      operationId: "deleteCustomer"
      produces:
        - "application/json"
      parameters:
        - in: "path"
          name: "id"
          schema:
            type: string
          required: true
          description: "Unique identifier of the Customer"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Customer deleted successfully."
        "400":
          description: "Invalid Customer by ID."
        "500":
          description: "Internal server error."
