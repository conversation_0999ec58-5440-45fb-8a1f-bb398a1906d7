const { emailSubjects, emailTemplate } = require('../../config/options');
const { triggerEmail } = require('../helpers/EmailHelper');
const { generateHtml } = require('../helpers/UtilHelper');

exports.sendResetPasswordOTPEmail = async (data) => {
  let htmlContent = generateHtml(
    'reset-password-otp-email',
    emailTemplate['reset-password-otp-email'](data)
  );
  await triggerEmail({
    to: data.email,
    subject: emailSubjects.ACCOUNT_PASSWORD_RESET,
    htmlContent,
    attachments: [],
  });
};
