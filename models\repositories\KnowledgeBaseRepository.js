const { KnowledgeBase, User } = require('..');
const {
  successMessage,
  knowledgeBaseStatus,
  usersRoles,
  errorMessage,
} = require('../../config/options');
const sequelize = require('sequelize');
const { Op } = sequelize;

const generateArticleCode = async () => {
  const lastKnowledgeBase = await KnowledgeBase.findOne({
    where: {
      status: {
        [Op.in]: [
          knowledgeBaseStatus.ACTIVE,
          knowledgeBaseStatus.INACTIVE,
          knowledgeBaseStatus.DRAFT,
          knowledgeBaseStatus.DELETED,
        ],
      },
    },
    order: [['createdAt', 'DESC']],
    attributes: ['articleCode'],
  });

  let nextNumber = 1000;

  if (lastKnowledgeBase && lastKnowledgeBase.articleCode) {
    const match = lastKnowledgeBase.articleCode.match(/\d+$/);
    if (match) {
      const lastNumber = parseInt(match[0], 10);
      if (lastNumber >= 1000) {
        nextNumber = lastNumber + 1;
      }
    }
  }

  return `AR${nextNumber}`;
};

const createKnowledgeBase = async (userId, body) => {
  try {
    const articleCode = await generateArticleCode();

    const knowledgeBase = await KnowledgeBase.create({
      ...body,
      articleCode,
      createdBy: userId,
    });

    return {
      success: true,
      message: successMessage.SAVED_SUCCESS_MESSAGE('Knowledge base'),
      data: knowledgeBase,
    };
  } catch (error) {
    throw new Error(error);
  }
};

const getKnowledgeBaseAndCount = async ({
  start = 0,
  limit = 10,
  search,
  status,
}) => {
  try {
    const where = {
      ...(search && {
        [Op.or]: [{ title: { [Op.iLike]: `%${search}%` } }],
      }),
      status: status || {
        [Op.in]: [
          knowledgeBaseStatus.ACTIVE,
          knowledgeBaseStatus.INACTIVE,
          knowledgeBaseStatus.DRAFT,
        ],
      },
    };

    const { count, rows } = await KnowledgeBase.findAndCountAll({
      where,
      offset: Number(start),
      limit: Number(limit),
      order: [['createdAt', 'DESC']],
      attributes: [
        'id',
        'articleCode',
        'title',
        'updatedAt',
        'status',
        'createdAt',
      ],
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'firstName', 'lastName'],
        },
      ],
    });

    return {
      message: successMessage.DETAIL_MESSAGE('Knowledge Base'),
      data: {
        rows,
        pagination: {
          totalCount: count,
          start: Number(start),
          limit: Number(limit),
        },
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

const getActiveKnowledgeBaseAndCount = async ({
  start = 0,
  limit = 10,
  userType = usersRoles.CUSTOMER,
}) => {
  try {
    const { count, rows } = await KnowledgeBase.findAndCountAll({
      where: { userType },
      offset: Number(start),
      limit: Number(limit),
      order: [['createdAt', 'DESC']],
      attributes: ['id', 'title', 'thumbnail', 'description'],
    });

    return {
      message: successMessage.DETAIL_MESSAGE('Knowledge Base'),
      data: {
        rows,
        pagination: {
          totalCount: count,
          start: Number(start),
          limit: Number(limit),
        },
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndUpdateStatus = async (knowledgeBaseId, isDeleted = false) => {
  try {
    const existingKnowledgeBase = await KnowledgeBase.findOne({
      where: {
        id: knowledgeBaseId,
      },
      attributes: ['id', 'title', 'status'],
    });

    if (!existingKnowledgeBase) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('knowledge base'),
      };
    }
    if (isDeleted) {
      existingKnowledgeBase.status = knowledgeBaseStatus.DELETED;
    } else {
      existingKnowledgeBase.status =
        knowledgeBaseStatus.ACTIVE === existingKnowledgeBase.status
          ? knowledgeBaseStatus.INACTIVE
          : knowledgeBaseStatus.ACTIVE;
    }
    await existingKnowledgeBase.save();

    return {
      success: true,
      message: successMessage.CHANGED_SUCCESS_MESSAGE('Knowledge base status'),
      ...(!isDeleted && {
        data: existingKnowledgeBase,
      }),
    };
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndPatchKnowledgeBaseStatus = async (id, isDelete) => {
  try {
    const { success, message } = await checkAndUpdateStatus(id, isDelete);
    return {
      success,
      message,
    };
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndUpdateKnowledgeBase = async (knowledgeBaseId, body) => {
  try {
    const existingKnowledgeBase = await KnowledgeBase.findOne({
      where: {
        id: knowledgeBaseId,
      },
    });

    if (!existingKnowledgeBase) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Knowledge base'),
      };
    }

    existingKnowledgeBase.title = body.title;
    existingKnowledgeBase.description = body.description;
    existingKnowledgeBase.thumbnail = body.thumbnail;
    existingKnowledgeBase.userType = body.userType;

    await existingKnowledgeBase.save();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Knowledge base'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndGetKnowledgeBase = async (id) => {
  const existingKnowledgeBase = await KnowledgeBase.findOne({
    where: {
      id,
    },
    attributes: [
      'id',
      'articleCode',
      'title',
      'description',
      'thumbnail',
      'updatedAt',
      'createdAt',
    ],
    include: [
      {
        model: User,
        as: 'user',
        attributes: ['firstName', 'lastName'],
      },
    ],
  });

  if (!existingKnowledgeBase) {
    return {
      success: false,
      message: errorMessage.DOES_NOT_EXIST('Knowledge base'),
    };
  }

  return {
    success: true,
    data: existingKnowledgeBase,
    message: successMessage.DETAIL_MESSAGE('Knowledge base'),
  };
};

module.exports = {
  createKnowledgeBase,
  getKnowledgeBaseAndCount,
  getActiveKnowledgeBaseAndCount,
  checkAndPatchKnowledgeBaseStatus,
  checkAndUpdateKnowledgeBase,
  checkAndGetKnowledgeBase,
};
