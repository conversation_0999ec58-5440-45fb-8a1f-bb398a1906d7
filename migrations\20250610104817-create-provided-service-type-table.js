'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('ProvidedServiceType', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      providedServiceId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'ProvidedService',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      serviceTypeId: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'ServiceType',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      minPrice: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true,
      },
      maxPrice: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('ProvidedServiceType');
  },
};
