const { serviceInterval } = require('../../config/options');

const validateCreateSubService = {
  serviceTypeId: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'serviceTypeId cannot be empty',
    },
    isUUID: {
      errorMessage: 'serviceTypeId must be a valid UUID',
    },
  },

  name: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'name cannot be empty',
    },
    isString: {
      errorMessage: 'name must be a string',
    },
  },

  description: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'description must be a string',
    },
  },

  imageUrl: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'imageUrl must be a string',
    },
  },

  serviceOptions: {
    in: ['body'],
    optional: true,
    isArray: {
      errorMessage: 'serviceOptions must be an array',
    },
  },

  'serviceOptions.*.name': {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Service option name is required',
    },
    isString: {
      errorMessage: 'Service option name must be a string',
    },
  },

  'serviceOptions.*.serviceIntervalType': {
    in: ['body'],
    notEmpty: {
      errorMessage: 'serviceIntervalType is required',
    },
    isString: {
      errorMessage: 'serviceIntervalType must be a string',
    },
    isIn: {
      options: [[serviceInterval.DAYS, serviceInterval.MILES]],
      errorMessage: `Service interval type must be one of: ${serviceInterval.DAYS}, ${serviceInterval.MILES}`,
    },
  },

  'serviceOptions.*.serviceIntervalValue': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Service interval value must be a string',
    },
  },
};

const validateUpdateSubService = {
  serviceTypeId: {
    in: ['body'],
    optional: true,
    trim: true,
    isUUID: {
      errorMessage: 'serviceTypeId must be a valid UUID',
    },
  },

  name: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'name must be a string',
    },
  },

  description: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'description must be a string',
    },
  },

  imageUrl: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'imageUrl must be a string',
    },
  },
};

module.exports = {
  validateCreateSubService,
  validateUpdateSubService,
};
