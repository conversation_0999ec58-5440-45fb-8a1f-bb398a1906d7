const ServiceRepository = require('../../../../models/repositories/ServiceRepository');
const { genRes, errorMessage, resCode } = require('../../../../config/options');

exports.getServiceListing = async (req, res) => {
  try {
    const { message, data } = await ServiceRepository.getServices();

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};
