const CustomerRepository = require('../../../../models/repositories/CustomerRepository');
const {
  genRes,
  errorMessage,
  resCode,
  errorTypes,
} = require('../../../../config/options');

exports.getCustomerListing = async (req, res) => {
  try {
    const {
      start = 0,
      limit = 10,
      search = null,
      fromDate,
      toDate,
    } = req.query;
    const { message, data } = await CustomerRepository.getAllCustomerAndCount({
      start: parseInt(start),
      limit: parseInt(limit),
      search,
      fromDate,
      toDate,
    });

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.getCustomerById = async (req, res) => {
  try {
    const customerId = req.params.id;

    const { success, message, data } =
      await CustomerRepository.checkAndGetCustomerById(customerId);

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.patchChangeStatusCustomer = async (req, res) => {
  try {
    const { success, message } =
      await CustomerRepository.checkAndPatchCustomerStatus(
        req.params.id,
        false
      );
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(
            resCode.HTTP_BAD_REQUEST,
            message,
            errorTypes.ACCESS_DENIED_EXCEPTION
          )
        );
    }
    return res
      .status(resCode.HTTP_CREATE)
      .json(genRes(resCode.HTTP_CREATE, { message }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};

exports.deleteCustomer = async (req, res) => {
  try {
    const { success, message } =
      await CustomerRepository.checkAndPatchCustomerStatus(req.params.id, true);
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(
            resCode.HTTP_BAD_REQUEST,
            message,
            errorTypes.ACCESS_DENIED_EXCEPTION
          )
        );
    }
    return res
      .status(resCode.HTTP_CREATE)
      .json(genRes(resCode.HTTP_CREATE, { message }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};
