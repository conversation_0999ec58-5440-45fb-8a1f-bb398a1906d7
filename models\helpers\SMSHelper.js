const chalk = require('chalk');

exports.triggerSMS = (data) => {
  if (['local', 'development', 'test'].includes(process.env.NODE_ENV)) {
    return;
  }
  if (!data && !data.mobileNumber) return;
  return new Promise((resolve, reject) => {
    const accountSid = process.env.SMS_ACCOUNT_SID;
    const authToken = process.env.SMS_AUTH_TOKEN;
    const from = process.env.SMS_FROM;
    const client = require('twilio')(accountSid, authToken);
    if (['development'].includes(process.env.NODE_ENV)) {
      client.logLevel = 'debug';
    }
    client.messages
      .create({
        body: data.message,
        from,
        to: `+${data.mobileNumber}`,
      })
      .then((message) => {
        console.log(
          chalk.green('✓'),
          'SMS sent to user with mobile number',
          data.mobileNumber
        );
        resolve(message);
      })
      .catch((error) => {
        console.error(
          chalk.red('X'),
          'Twi<PERSON>: Unable to send SMS to user with mobile number',
          data.mobileNumber
        );
        customErrorLogger(error);
        // reject(error);
      });
  });
};
