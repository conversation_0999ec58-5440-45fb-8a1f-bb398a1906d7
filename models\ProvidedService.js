module.exports = (sequelize, DataTypes) => {
  const ProvidedService = sequelize.define(
    'ProvidedService',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      note: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
    },
    {
      tableName: 'ProvidedService',
      timestamps: true,
    }
  );

  ProvidedService.associate = function (models) {
    ProvidedService.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE',
    });

    ProvidedService.hasMany(models.ProvidedServiceType, {
      foreignKey: 'providedServiceId',
      as: 'serviceTypes',
      onDelete: 'CASCADE',
    });
  };

  return ProvidedService;
};
