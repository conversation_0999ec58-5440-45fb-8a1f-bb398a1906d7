const UtilHelper = require('../models/helpers/UtilHelper');

const options = {
  randomUsernameSize: 10,
  jwtTokenExpiry: '1d',
  defaultOTP: 5555,
  refreshTokenExpiryTime: 300,
  userNamePrefix: 'RO',
  otpExpireInDays: 1,
  signedUrlExpireSeconds: 3000,
  defaultCountry: 'India',
  userStatus: {
    ON_BOARDED: 'on_boarded',
    PENDING_VERIFICATION: 'pending_verification',
    ACTIVE: 'active',
    BLOCKED: 'blocked',
    DELETED: 'deleted',
  },
  vehicleStatus: {
    ACTIVE: 'active',
    DELETED: 'deleted',
  },
  serviceStatus: {
    ACTIVE: 'active',
    INACTIVE: 'inactive',
    DELETED: 'deleted',
    BLOCKED: 'blocked',
  },
  serviceAppointmentUpdateType: {
    CANCEL: 'cancel',
    CONFIRM: 'confirm',
    RESCHEDULE: 'reschedule',
    START: 'start',
    CLOSE: 'close',
  },
  serviceRequestStatus: {
    PENDING: 'pending',
    CONFIRMED: 'confirmed',
    CANCELLED: 'cancelled',
    STARTED: 'started',
    COMPLETED: 'completed',
  },
  serviceRequestProviderStatus: {
    PENDING: 'pending',
    CLOSED: 'closed',
    ACCEPTED: 'accepted',
    REJECTED: 'rejected',
  },
  knowledgeBaseStatus: {
    ACTIVE: 'active',
    INACTIVE: 'inactive',
    DRAFT: 'draft',
    DELETED: 'deleted',
  },
  dealsOnWheelsStatus: {
    ACTIVE: 'active',
    INACTIVE: 'inactive',
    DELETED: 'deleted',
  },
  fileType: {
    IMAGE: 'image',
    VIDEO: 'video',
    PDF: 'pdf',
    DOC: 'doc',
  },
  fileCategory: {
    TECHNICALSKILL_CIRTIFICATE: 'technical_skill_certificate',
    DRIVERS_LICENSE: 'driving_license',
    INSURANCE_CIRTIFICATE: 'insurance_certificate',
    BUSINESS_LICENSE: 'business_license',
  },
  complianceLegalTitle: {
    TERMS_AND_CONDITIONS: 'terms_and_conditions',
    PRIVACY_POLICY: 'privacy_policy',
  },
  dayOfWeek: {
    MONDAY: 'monday',
    TUESDAY: 'tuesday',
    WEDNESDAY: 'wednesday',
    THURSDAY: 'thursday',
    FRIDAY: 'friday',
    SATURDAY: 'saturday',
    SUNDAY: 'sunday',
  },
  addressType: {
    HOME: 'home',
    WORK: 'work',
    OTHER: 'other',
  },
  serviceInterval: {
    MILES: 'miles',
    DAYS: 'days',
  },
  propertyLocation: {
    HOUSE: 'house',
    GARAGE: 'garage',
    DRIVEWAY: 'driveway',
    STREET: 'street',
    PARKING_GARAGE: 'parking_garage',
    HIGHWAY_LEFT_SHOULDER: 'highway_left_shoulder',
    HIGHWAY_RIGHT_SHOULDER: 'highway_right_shoulder',
    OTHER: 'other',
  },
  serviceDuration: {
    URGENT: 'urgent',
    WITHIN_TWOWEEKS: 'within_two_weeks',
    NO_RUSH: 'no_rush',
    MORETHAN_TWO_WEEKS: 'morethan_two-weeks',
  },
  emailSubjects: {
    OTP: 'OTP',
    ACCOUNT_PASSWORD_RESET: 'Reset password OTP',
  },
  genNotificationMessage: {},
  emailTypes: {
    EMAIL_OTP_VERIFICATION: 'email_otp_verification',
    EMAIL_REGISTERED_SUCCESSFULLY: 'email_registered_successfully',
    EMAIL_SKILL_VERIFICATION: 'email_skill_verification',
    EMAIL_PROFILE_APPROVED: 'email_profile_approved',
    EMAIL_ACCOUNT_BLOCKED: 'email_account_blocked',
    EMAIL_PROFILE_REJECTED: 'email_profile_rejected',
    EMAIL_MOBILE_NUMBER_CHANGED: 'email_mobile_number_changed',
    EMAIL_CHANGED: 'email_changed',
    EMAIL_RESET_PASSWORD_OTP: 'reset_password_otp_email',
  },
  emailTemplate: {
    'reset-password-otp-email': (data) => {
      return {
        title: 'Your OTP for password reset',
        tempOtp: data.tempOtp,
        mailMessage: {
          line1: `Your OTP for password reset`,
          line2: `Enter this OTP on the password reset page. OTP is `,
          line3: 'confidential, please do not share with anyone.',
        },
        logo: `${process.env.BACKEND_URL}/assets/logo.png`,
      };
    },
  },
  resCode: {
    HTTP_OK: 200,
    HTTP_CREATE: 201,
    HTTP_NO_CONTENT: 204,
    HTTP_BAD_REQUEST: 400,
    HTTP_UNAUTHORIZED: 401,
    HTTP_FORBIDDEN: 403,
    HTTP_NOT_FOUND: 404,
    HTTP_METHOD_NOT_ALLOWED: 405,
    HTTP_CONFLICT: 409,
    HTTP_INTERNAL_SERVER_ERROR: 500,
    HTTP_SERVICE_UNAVAILABLE: 503,
  },
  errorTypes: {
    OAUTH_EXCEPTION: 'OAuthException',
    ACCESS_DENIED_EXCEPTION: 'AccessDeniedException',
    ALREADY_AUTHENTICATED: 'AlreadyAuthenticated',
    UNAUTHORIZED_ACCESS: 'UnauthorizedAccess',
    FORBIDDEN: 'Forbidden',
    INPUT_VALIDATION: 'InputValidationException',
    ACCOUNT_ALREADY_EXIST: 'AccountAlreadyExistException',
    ACCOUNT_DOES_NOT_EXIST: 'AccountDoesNotExistException',
    ENTITY_NOT_FOUND: 'EntityNotFound',
    ACCOUNT_BLOCKED: 'AccountBlocked',
    ACCOUNT_DEACTIVATED: 'AccountDeactivated',
    CONTENT_BLOCKED: 'ContentBlocked',
    CONTENT_REMOVED: 'ContentRemoved',
    DUPLICATE_CONTENT: 'DuplicateContent',
    PRIVATE_CONTENT: 'PrivateContent',
    PRIVATE_ACCOUNT: 'PrivateAccount',
    DUPLICATE_REQUEST: 'DuplicateRequest',
    EMAIL_NOT_VERIFIED: 'emailNotVerified',
    MOBILE_NUMBER_NOT_VERIFIED: 'mobileNumberNotVerified',
    INTERNAL_SERVER_ERROR: 'InternalServerError',
    CATCH_ERRORS: 'Oops! something went wrong.',
  },
  errorMessage: {
    UNAUTHORIZED_ACCESS: 'Not authorized to perform this action',
    SERVER_ERROR: 'Oops! something went wrong.',
    INVALID_CREDENTIALS: 'The email and/or password entered are incorrect',
    OTP_INVALID: 'Invalid Otp',
    CONTACT_ADMIN: 'Contact admin to perform edit',
    COUNTRY_CODE: 'Please add country code',
    INCORRECT_DATA: (data) => `The ${data} entered is incorrect`,
    INVALID_REQUEST: 'Invalid Request',
    USER_ACCOUNT_BLOCKED: 'Your account has been blocked, Please contact admin',
    ROLE_INVALID_LOGIN: 'Account access denied',
    NO_USER: (data) => `User does not exists with this ${data}`,
    EXISTS_USER: (data) => `User exists with ${data}`,
    DOES_NOT_EXIST: (data) => `The ${data} does not exist`,
    ALREADY_EXIST: (data) => `The ${data} already exist`,
    INCORRECT_FILE_DATA: 'File contains invalid data',
    SAME_EMAIL_MOBILE_EXISTS: (data) => `User with same ${data} already exists`,
    DATA_NOT_FOUND: 'Data not found',
  },
  successMessage: {
    OTP_SEND: (type) => `An OTP has been send to your ${type}`,
    OTP_VERIFIED: (type) => `OTP has been verified`,
    LOG: (data) => `You have ${data} successfully`,
    UPDATE_SUCCESS_MESSAGE: (data) => `${data} updated successfully`,
    DELETE_SUCCESS_MESSAGE: (data) => `${data} deleted successfully`,
    REMOVED_SUCCESS_MESSAGE: (data) => `${data} removed successfully`,
    ADD_SUCCESS_MESSAGE: (data) => `${data} added successfully`,
    SAVED_SUCCESS_MESSAGE: (data) => `${data} saved successfully`,
    GENERATE_SUCCESS_MESSAGE: (data) => `${data} generate successfully`,
    CHANGED_SUCCESS_MESSAGE: (data) => `${data} changed successfully`,
    VERIFIED_SUCCESS_MESSAGE: (data) => `${data} verified successfully`,
    SEND_SUCCESS_MESSAGE: (data) => `${data} send successfully`,
    DETAIL_MESSAGE: (data) => `Fetched ${data} details successfully`,
    CREATED_MESSAGE: (data) => `${data} Created Successfully`,
  },
  usersRoles: {
    SUPER_ADMIN: 'SUPER_ADMIN',
    ADMIN: 'ADMIN',
    CUSTOMER: 'CUSTOMER',
    PROVIDER: 'PROVIDER',
    getAdminArray: () => [
      options.usersRoles.SUPER_ADMIN,
      options.usersRoles.ADMIN,
    ],
    getAllRolesAsArray: () => [
      options.usersRoles.SUPER_ADMIN,
      options.usersRoles.ADMIN,
      options.usersRoles.CUSTOMER,
      options.usersRoles.PROVIDER,
    ],
    getNonAdminArray: () => [
      options.usersRoles.CUSTOMER,
      options.usersRoles.PROVIDER,
    ],
  },
  genOtp: UtilHelper.genOtp,
  genRes: UtilHelper.genRes,
  otpExpireInMins: 5,
  generateCloudFrontUrl: UtilHelper.generateCloudFrontUrl,
  accessManagementType: {
    DASHBOARD: 'dashboard',
    ADMIN: 'admin',
    CUSTOMER: 'customer',
    PROVIDER: 'provider',
  },
  vehicleOptions: {
    types: ['Car', 'Motorcycle', 'Truck'],
    years: ['2020', '2021', '2022', '2023', '2024', '2025'],
    makes: {
      'Car-2020': ['Honda', 'Hyundai'],
      'Car-2021': ['Honda', 'Hyundai'],
      'Car-2022': ['Honda', 'Hyundai'],
      'Car-2023': ['Honda', 'Hyundai'],
      'Car-2024': ['Honda', 'Hyundai'],
      'Car-2025': ['Honda', 'Hyundai'],

      'Motorcycle-2020': ['Honda'],
      'Motorcycle-2021': ['Honda'],
      'Motorcycle-2022': ['Honda'],
      'Motorcycle-2023': ['Honda'],
      'Motorcycle-2024': ['Honda'],
      'Motorcycle-2025': ['Honda'],

      'Truck-2020': ['Ford', 'Chevrolet'],
      'Truck-2021': ['Ford', 'Chevrolet'],
      'Truck-2022': ['Ford', 'Chevrolet'],
      'Truck-2023': ['Ford', 'Chevrolet'],
      'Truck-2024': ['Ford', 'Chevrolet'],
      'Truck-2025': ['Ford', 'Chevrolet'],
    },
    models: {
      Honda: ['Accord', 'Civic', 'Pilot'],
      Hyundai: ['Santa Fe', 'Tucson'],
      Ford: ['F-150'],
      Chevrolet: ['Silverado'],
    },
    trims: {
      Accord: ['Base', 'Sport', 'EX'],
      Civic: ['Base', 'Touring'],
      Pilot: ['EX-L', 'Touring'],
    },
    engines: {
      Base: ['2.0L I4', '2.5L I4'],
      Sport: ['3.0L V6'],
      EX: ['3.5L V6'],
      Touring: ['4.0L V8'],
      'EX-L': ['5.0L V8'],
    },
    drivetrains: [
      'Automatic',
      'Manual',
      'CVT',
      'Dual-Clutch',
      'Semi-Automatic',
    ],
  },
  locationType: {
    GARAGE: 'garage',
  },
  elevenLabs: {
    baseUrl: process.env.ELEVENLABS_BASE_URL || 'https://api.elevenlabs.io/v1',
    defaultVoiceId:
      process.env.ELEVENLABS_DEFAULT_VOICE_ID || 'EXAVITQu4vr4xnSDxMaL',
  },
  chatRole: {
    USER: 'user',
    ASSISTANT: 'assistant',
  },
};

module.exports = options;
