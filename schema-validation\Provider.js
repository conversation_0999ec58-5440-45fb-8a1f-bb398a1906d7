exports.getProviders = {
  latitude: {
    in: ['query'],
    notEmpty: {
      errorMessage: 'Latitude is required',
    },
    isFloat: {
      errorMessage: 'Latitude must be a number',
    },
  },
  longitude: {
    in: ['query'],
    notEmpty: {
      errorMessage: 'Longitude is required',
    },
    isFloat: {
      errorMessage: 'Longitude must be a number',
    },
  },
  serviceTypeId: {
    in: ['query'],
    optional: true,
    isUUID: {
      errorMessage: 'serviceTypeId must be a valid UUID',
    },
  },
  radius: {
    in: ['query'],
    optional: true,
    isFloat: {
      errorMessage: 'Radius must be a number',
    },
  },
  start: {
    in: ['query'],
    optional: true,
    isInt: {
      errorMessage: 'Start must be an integer',
    },
  },
  limit: {
    in: ['query'],
    optional: true,
    isInt: {
      errorMessage: 'Limit must be an integer',
    },
  },
};
