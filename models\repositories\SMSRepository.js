const chalk = require('chalk');
const { triggerSMS } = require('../helpers/SMSHelper');

exports.sendMobileOtp = async (user, otp) => {
  const payload = {
    mobileNumber: `${user.countryCode}${user.mobileNumber}`,
    message: `Your verification otp is ${otp}`,
  };
  try {
    await triggerSMS(payload);
  } catch (error) {
    console.error(
      chalk.red('X'),
      'Unable to send SMS to user with mobile number',
      payload.mobileNumber
    );
    throw new Error(error);
  }
};
