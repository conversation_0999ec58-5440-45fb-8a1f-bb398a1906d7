const { vehicleStatus } = require('../config/options');
const { Op } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  const Vehicle = sequelize.define(
    'Vehicle',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      vehicleType: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      year: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      industry: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      model: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      vehicleTrim: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      engine: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      driveTrain: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      milage: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      vin: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      plateNumber: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      state: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      status: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: vehicleStatus.ACTIVE,
      },
    },
    {
      tableName: 'Vehicle',
      timestamps: true,
      defaultScope: {
        where: {
          status: {
            [Op.ne]: vehicleStatus.DELETED,
          },
        },
      },

      scopes: {
        withDeleted: {
          // No conditions here to include all records including deleted
        },
        onlyDeleted: {
          where: {
            status: vehicleStatus.DELETED,
          },
        },
      },
    }
  );

  Vehicle.associate = function (models) {
    Vehicle.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
    });
  };

  return Vehicle;
};
