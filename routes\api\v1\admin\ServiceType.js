const { Router } = require('express');

const router = Router();
const { checkSchema } = require('express-validator');

const {
  postServiceType,
  putServiceType,
  getServiceTypeListing,
  patchChangeStatusServiceType,
  deleteServiceType,
} = require('../../../../controllers/api/v1/admin/ServiceType');

const {
  requestValidator,
} = require('../../../../models/helpers/ErrorHandleHelper');
const {
  createAndUpdateServiceType,
} = require('../../../../schema-validation/admin/ServiceType');

router.post(
  '/',
  checkSchema(createAndUpdateServiceType),
  requestValidator,
  postServiceType
);

router.put(
  '/:id',
  checkSchema(createAndUpdateServiceType),
  requestValidator,
  putServiceType
);

router.get('/', getServiceTypeListing);

router.patch('/:id', patchChangeStatusServiceType);

router.delete('/:id', deleteServiceType);

module.exports = router;
