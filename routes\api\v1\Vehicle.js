const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();

const VehicleControl = require('../../../controllers/api/v1/Vehicle');
const VehicleSchema = require('../../../schema-validation/Vehicle');
const ErrorHandleHelper = require('../../../models/helpers/ErrorHandleHelper');

router.post(
  '/',
  checkSchema(VehicleSchema.createOrUpdateVehicle),
  ErrorHandleHelper.requestValidator,
  VehicleControl.postVehicle
);

router.post(
  '/vin',
  checkSchema(VehicleSchema.createVehicleFromVin),
  ErrorHandleHelper.requestValidator,
  VehicleControl.postVehicleDetailsFromVin
);

router.put(
  '/:id',
  checkSchema(VehicleSchema.createOrUpdateVehicle),
  ErrorHandleHelper.requestValidator,
  VehicleControl.putVehicle
);

router.get(
  '/',
  ErrorHandleHelper.requestValidator,
  VehicleControl.getVehicleListing
);

router.get(
  '/:id',
  ErrorHandleHelper.requestValidator,
  VehicleControl.getVehicleById
);

router.delete('/:id', VehicleControl.deleteVehicle);

router.get('/dropdown/options', VehicleControl.getVehicleOptions);

module.exports = router;
