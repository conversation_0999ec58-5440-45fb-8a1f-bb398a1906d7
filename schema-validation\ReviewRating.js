exports.createRatingAndReview = {
  serviceRequestId: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'serviceRequest ID is required',
    },
    isInt: {
      errorMessage: 'serviceRequest ID must be an integer',
    },
    toInt: true,
  },
  rating: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Rating is required',
    },
    isFloat: {
      options: { min: 0, max: 5 },
      errorMessage: 'Rating must be a decimal between 0 and 5',
    },
    toFloat: true,
  },
  review: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'Review must be a string',
    },
  },
};
