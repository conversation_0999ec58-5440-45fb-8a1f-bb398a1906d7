module.exports = (sequelize, DataTypes) => {
  const ProvidedServiceCancellationPolicy = sequelize.define(
    'ProvidedServiceCancellationPolicy',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      cancellationFee: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
      },
      startTime: {
        type: DataTypes.TIME,
        allowNull: true,
      },
      endTime: {
        type: DataTypes.TIME,
        allowNull: true,
      },
    },
    {
      tableName: 'ProvidedServiceCancellationPolicy',
      timestamps: true,
    }
  );

  ProvidedServiceCancellationPolicy.associate = function (models) {
    ProvidedServiceCancellationPolicy.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE',
    });
  };

  return ProvidedServiceCancellationPolicy;
};
