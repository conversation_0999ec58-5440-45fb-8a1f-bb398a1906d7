const sequelize = require('sequelize');
const { BusinessInformation, User } = require('..');
const {
  successMessage,
  errorMessage,
  userStatus,
} = require('../../config/options');
const { Op } = sequelize;

const checkDuplicateBusinessName = async (businessName, excludeId = null) => {
  if (!businessName) return { success: true };

  const where = {
    businessName,
    ...(excludeId && { id: { [Op.ne]: excludeId } }),
  };

  const existingBusiness = await BusinessInformation.findOne({ where });

  if (existingBusiness) {
    return {
      success: false,
      message: errorMessage.ALREADY_EXIST('Business Name'),
    };
  }

  return { success: true };
};

const checkAndCreateBusinessInfo = async (userId, body) => {
  try {
    const { success, message } = await checkDuplicateBusinessName(
      body.businessName
    );

    if (!success) {
      return {
        success: false,
        message,
      };
    }

    const businessInfo = await BusinessInformation.create({
      ...body,
      userId,
    });

    await User.update(
      { status: userStatus.PENDING_VERIFICATION },
      { where: { id: userId } }
    );

    return {
      success: true,
      message: successMessage.SAVED_SUCCESS_MESSAGE('Business Information'),
      data: businessInfo,
    };
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndUpdateBusinessInfo = async (userId, businessInfoId, body) => {
  try {
    const existingBusinessInfo = await BusinessInformation.findOne({
      where: {
        id: businessInfoId,
        userId,
      },
    });

    if (!existingBusinessInfo) {
      return {
        success: false,
        message: errorMessage.NOT_FOUND('Business information'),
      };
    }

    if (
      body.businessName &&
      body.businessName !== existingBusinessInfo.businessName
    ) {
      const { success, message } = await checkDuplicateBusinessName(
        body.businessName,
        businessInfoId
      );
      if (!success) {
        return {
          success: false,
          message,
        };
      }
    }

    Object.keys(body).forEach((key) => {
      if (body[key] !== undefined) {
        existingBusinessInfo[key] = body[key];
      }
    });

    await existingBusinessInfo.save();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Business information'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndGetBusinessInfo = async (userId) => {
  const existingBusinessInfo = await BusinessInformation.findOne({
    where: {
      userId,
    },
    attributes: [
      'id',
      'businessLogo',
      'businessName',
      'yearOfExperience',
      'hasCertificationLicense',
      'isInsured',
      'hasToolset',
    ],
  });

  if (!existingBusinessInfo) {
    return {
      success: false,
      message: errorMessage.DOES_NOT_EXIST('Business information'),
    };
  }

  return {
    success: true,
    data: existingBusinessInfo,
    message: successMessage.DETAIL_MESSAGE('Business information'),
  };
};

module.exports = {
  checkAndCreateBusinessInfo,
  checkAndUpdateBusinessInfo,
  checkAndGetBusinessInfo,
};
