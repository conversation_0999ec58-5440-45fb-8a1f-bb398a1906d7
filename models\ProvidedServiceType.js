module.exports = (sequelize, DataTypes) => {
  const ProvidedServiceType = sequelize.define(
    'ProvidedServiceType',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      minPrice: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
      },
      maxPrice: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
      },
    },
    {
      tableName: 'ProvidedServiceType',
      timestamps: true,
    }
  );

  ProvidedServiceType.associate = function (models) {
    ProvidedServiceType.belongsTo(models.ProvidedService, {
      foreignKey: 'providedServiceId',
      as: 'providedService',
      onDelete: 'CASCADE',
    });

    ProvidedServiceType.belongsTo(models.ServiceType, {
      foreignKey: 'serviceTypeId',
      as: 'serviceType',
      onDelete: 'CASCADE',
    });
  };

  return ProvidedServiceType;
};
