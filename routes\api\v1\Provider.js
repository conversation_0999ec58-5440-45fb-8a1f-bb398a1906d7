const express = require('express');
const { checkSchema } = require('express-validator');
const router = express.Router();
const ProviderControl = require('../../../controllers/api/v1/Provider');
const ProviderSchema = require('../../../schema-validation/Provider');
const ErrorHandleHelper = require('../../../models/helpers/ErrorHandleHelper');

router.get(
  '/',
  checkSchema(ProviderSchema.getProviders),
  ErrorHandleHelper.requestValidator,
  ProviderControl.getProviderListing
);

router.get('/:id', ProviderControl.getProviderById);

router.get(
  '/dashboard/service-count',
  ProviderControl.getProviderDashboardServiceCount
);

module.exports = router;
