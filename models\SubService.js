'use strict';

const { serviceStatus, generateCloudFrontUrl } = require('../config/options');

const { Op } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  const SubService = sequelize.define(
    'SubService',
    {
      id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      imageUrl: {
        allowNull: true,
        type: DataTypes.TEXT,
        get() {
          return generateCloudFrontUrl(this.getDataValue('imageUrl'));
        },
        set(file) {
          if (file) {
            this.setDataValue(
              'imageUrl',
              `uploads/${file.split('uploads/')[1]}`
            );
          }
        },
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      externalLink: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      status: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: serviceStatus.ACTIVE,
      },
    },
    {
      tableName: 'SubService',
      timestamps: true,
      defaultScope: {
        where: {
          status: {
            [Op.ne]: serviceStatus.DELETED,
          },
        },
      },

      scopes: {
        withDeleted: {
          // No conditions here to include all records including deleted
        },
        onlyDeleted: {
          where: {
            status: serviceStatus.DELETED,
          },
        },
      },
    }
  );

  SubService.associate = function (models) {
    SubService.belongsTo(models.ServiceType, {
      foreignKey: 'serviceTypeId',
      as: 'serviceType',
    });
  };

  return SubService;
};
