const db = require('../index');
const { BusinessDocument } = require('..');
const { successMessage, errorMessage } = require('../../config/options');

const checkAndCreateBusinessDocument = async (businessId, documents = []) => {
  const transaction = await db.sequelize.transaction();
  try {
    const docs = Array.isArray(documents) ? documents : [];

    if (!businessId) {
      await transaction.rollback();
      return {
        message: errorMessage.DOES_NOT_EXIST('Business Information'),
      };
    }

    const payload = docs.map((docData) => ({
      ...docData,
      businessId,
    }));

    const data = await BusinessDocument.bulkCreate(payload, { transaction });

    await transaction.commit();
    return {
      success: true,
      message: successMessage.SAVED_SUCCESS_MESSAGE('Business Documents'),
      data,
    };
  } catch (error) {
    await transaction.rollback();
    throw new Error(error);
  }
};

const checkAndUpdateBusinessDocument = async (businessId, documents = []) => {
  const transaction = await db.sequelize.transaction();
  try {
    const docs = Array.isArray(documents) ? documents : [];

    if (!businessId) {
      await transaction.rollback();
      return {
        message: errorMessage.DOES_NOT_EXIST('Business Information'),
      };
    }

    for (const doc of docs) {
      if (!doc.id) continue;

      const existingDoc = await BusinessDocument.findOne({
        where: { id: doc.id, businessId },
        transaction,
      });

      if (existingDoc) {
        Object.keys(doc).forEach((key) => {
          if (key !== 'id') {
            existingDoc[key] = doc[key];
          }
        });

        await existingDoc.save({ transaction });
      }
    }

    await transaction.commit();
    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Business Documents'),
    };
  } catch (error) {
    await transaction.rollback();
    throw new Error(error);
  }
};

const checkAndGetBusinessDocuments = async (businessId) => {
  try {
    const documents = await BusinessDocument.findAll({
      where: { businessId },
      order: [['id', 'ASC']],
      attributes: [
        'id',
        'fileName',
        'filePath',
        'fileType',
        'fileSize',
        'fileCategory',
      ],
    });

    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE('Business Documents'),
      data: documents,
    };
  } catch (error) {
    throw new Error(error);
  }
};

module.exports = {
  checkAndCreateBusinessDocument,
  checkAndUpdateBusinessDocument,
  checkAndGetBusinessDocuments,
};
