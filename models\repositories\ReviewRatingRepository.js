const { ReviewRating, ServiceRequest, User } = require('..');
const { successMessage, errorMessage } = require('../../config/options');
const sequelize = require('sequelize');

const checkAndCreateReviewRating = async (userId, body) => {
  try {
    const { serviceRequestId, rating, review } = body;

    const serviceRequest = await ServiceRequest.findOne({
      where: { id: serviceRequestId },
    });

    if (!serviceRequest) {
      return {
        success: false,
        message: errorMessage.NO_USER('Service Request'),
      };
    }

    const providerId = serviceRequest.providerId;

    const reviewRating = await ReviewRating.create({
      userId,
      serviceRequestId,
      rating,
      review,
    });

    const ratingsData = await ReviewRating.findAll({
      include: [
        {
          model: ServiceRequest,
          as: 'serviceRequest',
          where: { providerId },
          attributes: [],
          required: true,
        },
      ],
      attributes: [
        [
          sequelize.fn(
            'ROUND',
            sequelize.fn('AVG', sequelize.col('rating')),
            2
          ),
          'averageRating',
        ],
      ],
      raw: true,
    });

    const avgRating = parseFloat(ratingsData?.[0]?.averageRating || 0).toFixed(
      2
    );

    await User.update({ avgRating }, { where: { id: providerId } });

    return {
      success: true,
      message: successMessage.SAVED_SUCCESS_MESSAGE('Review'),
      data: reviewRating,
    };
  } catch (error) {
    throw new Error(error);
  }
};

const getReviewRatingCount = async ({ userId, start = 0, limit = 10 }) => {
  try {
    const { count: totalCount, rows } = await ReviewRating.findAndCountAll({
      offset: Number(start),
      limit: Number(limit),
      order: [['createdAt', 'DESC']],
      attributes: ['id', 'rating', 'review', 'createdAt'],
      include: [
        {
          model: ServiceRequest,
          as: 'serviceRequest',
          where: { providerId: userId },
          attributes: [],
          required: true,
        },
        {
          model: User,
          as: 'user',
          attributes: ['id', 'firstName', 'lastName', 'profilePicture'],
        },
      ],
    });

    return {
      message: successMessage.DETAIL_MESSAGE('Review'),
      data: {
        rows,
        pagination: {
          totalCount,
          start: Number(start),
          limit: Number(limit),
        },
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

const getReviewRatingStats = async (userId) => {
  try {
    const ratingsCount = await ReviewRating.findOne({
      include: [
        {
          model: ServiceRequest,
          as: 'serviceRequest',
          where: { providerId: userId },
          attributes: [],
          required: true,
        },
      ],
      attributes: [
        [
          sequelize.fn(
            'ROUND',
            sequelize.fn('AVG', sequelize.col('rating')),
            2
          ),
          'averageRating',
        ],
        [
          sequelize.fn('COUNT', sequelize.col('ReviewRating.id')),
          'totalRatingCount',
        ],
      ],
      raw: true,
    });

    const ratingPercent = await ReviewRating.findAll({
      include: [
        {
          model: ServiceRequest,
          as: 'serviceRequest',
          where: { providerId: userId },
          attributes: [],
          required: true,
        },
      ],
      attributes: [
        'rating',
        [sequelize.fn('COUNT', sequelize.col('rating')), 'count'],
      ],
      group: ['rating'],
      order: [['rating', 'DESC']],
      raw: true,
    });

    const ratings = [
      { rating: 5, count: 0 },
      { rating: 4, count: 0 },
      { rating: 3, count: 0 },
      { rating: 2, count: 0 },
      { rating: 1, count: 0 },
    ];

    for (let i = 0; i < ratings.length; i++) {
      let element = ratingPercent.find(
        (item) => parseInt(item.rating, 10) === ratings[i].rating
      );
      if (element) {
        element = element.toJSON?.() || element;
        ratings[i].count = parseInt(element.count, 10);
      }
    }

    return {
      message: successMessage.DETAIL_MESSAGE('Rating Stats'),
      data: {
        averageRating: parseFloat(ratingsCount?.averageRating || 0).toFixed(2),
        totalRatingCount: parseInt(ratingsCount?.totalRatingCount || 0),
        ratingDistribution: ratings,
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

module.exports = {
  checkAndCreateReviewRating,
  getReviewRatingCount,
  getReviewRatingStats,
};
