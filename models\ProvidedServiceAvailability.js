const { dayOfWeek } = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const ProvidedServiceAvailability = sequelize.define(
    'ProvidedServiceAvailability',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      dayOfWeek: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: dayOfWeek.MONDAY,
      },
    },
    {
      tableName: 'ProvidedServiceAvailability',
      timestamps: true,
    }
  );

  ProvidedServiceAvailability.associate = function (models) {
    ProvidedServiceAvailability.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE',
    });

    ProvidedServiceAvailability.hasMany(
      models.ProvidedServiceAvailabilitySlot,
      {
        foreignKey: 'availabilityId',
        as: 'slots',
        onDelete: 'CASCADE',
      }
    );
  };

  return ProvidedServiceAvailability;
};
