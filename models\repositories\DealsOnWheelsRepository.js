const { DealsOnWheels } = require('..');
const {
  successMessage,
  errorMessage,
  dealsOnWheelsStatus,
} = require('../../config/options');
const { Op } = require('sequelize');

const createDealsOnWheels = async (payload) => {
  try {
    const dealsOnWheels = await DealsOnWheels.create(payload);

    return {
      success: true,
      message: successMessage.SAVED_SUCCESS_MESSAGE('Deals On Wheels'),
      data: dealsOnWheels,
    };
  } catch (error) {
    throw new Error(error);
  }
};

const updateDealsOnWheels = async (id, payload) => {
  try {
    const dealsOnWheels =
      await DealsOnWheels.scope('activeOrInactive').findByPk(id);

    if (!dealsOnWheels) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Deals On Wheels'),
        data: null,
      };
    }
    Object.assign(dealsOnWheels, payload);

    await dealsOnWheels.save();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Deals On Wheels'),
      data: dealsOnWheels,
    };
  } catch (error) {
    throw new Error(error);
  }
};

const getDealsOnWheelsAndCount = async ({
  activeOnly = false,
  start = 0,
  limit = 10,
}) => {
  try {
    const where = !activeOnly
      ? {
          status: { [Op.ne]: dealsOnWheelsStatus.DELETED },
        }
      : {};
    const { count, rows } = await DealsOnWheels.findAndCountAll({
      where,
      offset: Number(start),
      limit: Number(limit),
      order: [['createdAt', 'DESC']],
    });

    return {
      message: successMessage.DETAIL_MESSAGE('Deals On Wheels'),
      data: {
        rows,
        pagination: {
          totalCount: count,
          start: Number(start),
          limit: Number(limit),
        },
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

module.exports = {
  createDealsOnWheels,
  updateDealsOnWheels,
  getDealsOnWheelsAndCount,
};
