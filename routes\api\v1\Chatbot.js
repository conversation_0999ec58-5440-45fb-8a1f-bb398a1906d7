const express = require('express');
const { checkSchema } = require('express-validator');
const router = express.Router();

const AuthHandler = require('../../../models/helpers/AuthHelper');
const ErrorHandleHelper = require('../../../models/helpers/ErrorHandleHelper');
const ChatbotSchema = require('../../../schema-validation/Chatbot');
const ChatbotControl = require('../../../controllers/api/v1/Chatbot');
const { usersRoles } = require('../../../config/options');

router.post(
  '/',
  AuthHandler.authenticateJWT([usersRoles.CUSTOMER, usersRoles.PROVIDER]),
  checkSchema(ChatbotSchema.chat),
  ErrorHandleHelper.requestValidator,
  ChatbotControl.chat
);

module.exports = router;
