const { ChatGoogleGenerativeAI } = require('@langchain/google-genai');
const {
  generalResponsePrompt,
} = require('../utils/prompts/GeneralConversation');

const llm = new ChatGoogleGenerativeAI({
  model: process.env.GEMINI_MODEL || 'gemini-2.0-flash',
  temperature: 0.1,
  apiKey: process.env.GOOGLE_API_KEY,
});

exports.generateResponse = async (input, history = '') => {
  try {
    const chain = generalResponsePrompt.pipe(llm);
    const result = await chain.invoke({ input, history });
    return typeof result === 'string' ? result : result?.content || result;
  } catch (error) {
    throw error;
  }
};
