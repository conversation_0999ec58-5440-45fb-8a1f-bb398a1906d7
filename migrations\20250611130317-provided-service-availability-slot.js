'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('ProvidedServiceAvailabilitySlot', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      availabilityId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'ProvidedServiceAvailability',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      startTime: {
        type: Sequelize.TIME,
        allowNull: false,
        defaultValue: '00:00:00',
      },
      endTime: {
        type: Sequelize.TIME,
        allowNull: false,
        defaultValue: '23:59:59',
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('ProvidedServiceAvailabilitySlot');
  },
};
