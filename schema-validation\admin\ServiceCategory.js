const createServiceCategory = {
  name: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'name cannot be empty',
    isString: {
      errorMessage: 'name must be string',
    },
  },
  imageUrl: {
    in: ['body'],
    trim: true,
    notEmpty: false,
    isString: {
      errorMessage: 'imageUrl  must be string',
    },
  },
};

const updateServiceCategory = {
  name: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'name must be string',
    },
  },
  imageUrl: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'imageUrl must be string',
    },
  },
};

module.exports = {
  createServiceCategory,
  updateServiceCategory,
};
