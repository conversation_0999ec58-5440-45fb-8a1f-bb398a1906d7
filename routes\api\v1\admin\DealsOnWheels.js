const { Router } = require('express');
const router = Router();
const {
  createDealsOnWheels,
  updateDealsOnWheels,
  getDealsOnWheelsList,
  updateDealsOnWheelsStatus,
} = require('../../../../controllers/api/v1/admin/DealsOnWheels');
const { checkSchema } = require('express-validator');

const ErrorHandleHelper = require('../../../../models/helpers/ErrorHandleHelper');
const {
  createOrUpdateDealsOnWheelsSchema,
  updateDealsOnWheelsStatusSchema,
} = require('../../../../schema-validation/admin/DealsOnWheels');

router.post(
  '/',
  checkSchema(createOrUpdateDealsOnWheelsSchema),
  ErrorHandleHelper.requestValidator,
  createDealsOnWheels
);

router.put(
  '/:id',
  checkSchema(createOrUpdateDealsOnWheelsSchema),
  ErrorHandleHelper.requestValidator,
  updateDealsOnWheels
);

router.patch(
  '/:id',
  checkSchema(updateDealsOnWheelsStatusSchema),
  ErrorHandleHelper.requestValidator,
  updateDealsOnWheelsStatus
);

router.get('/', getDealsOnWheelsList);

module.exports = router;
