paths:
  /admin/sub-service:
    post:
      tags:
        - "SubService"
      summary: "Create a new Sub Service"
      description: "Add a new Sub Service with the provided details."
      operationId: "createSubService"
      requestBody:
        description: "Payload containing Sub service details."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/createSubService"
        required: true
      produces:
        - "application/json"
      parameters: []
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Sub Service created successfully."
        "400":
          description: "Invalid request payload."
        "500":
          description: "Internal server error."

    get:
      tags:
        - "SubService"
      summary: "Get list of Sub service with pagination and search"
      description: "Fetch paginated list of Sub service with optional search filter."
      operationId: "listSubService"
      parameters:
        - name: "serviceCategoryId"
          in: "query"
          required: true
          description: "Service Category ID"
          schema:
            type: string
            format: uuid
        - name: "serviceTypeId"
          in: "query"
          required: true
          description: "Service Type ID"
          schema:
            type: string
            format: uuid
        - name: "start"
          in: "query"
          description: "Starting index for pagination"
          required: false
          schema:
            type: integer
            default: 0
        - name: "limit"
          in: "query"
          description: "Number of records to return"
          required: false
          schema:
            type: integer
            default: 10
        - name: "search"
          in: "query"
          description: "Search keyword to filter types by name"
          required: false
          schema:
            type: string
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "List of Sub service fetched successfully."
        "400":
          description: "Invalid request parameters."
        "500":
          description: "Internal server error."

  /admin/sub-service/{id}:
    put:
      tags:
        - "SubService"
      summary: "Update an existing Sub service"
      description: "Update the details of a specific Sub service by ID."
      operationId: "updateSubService"
      parameters:
        - name: id
          in: path
          required: true
          description: "ID of the Sub service to update"
          schema:
            type: string
      requestBody:
        description: "Payload containing updated Sub service details."
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/updateSubService"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Sub service updated successfully."
        "400":
          description: "Invalid request payload or duplicate name."
        "404":
          description: "Sub service not found."
        "500":
          description: "Internal server error."

    patch:
      tags:
        - "SubService"
      summary: "Update SubService status by ID"
      description: "update a SubService status using its ID"
      operationId: "updateSubServiceStatus"
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: "ID of the sub service  to update status"
          schema:
            type: string
      responses:
        "201":
          description: "Sub service status updated successfully"
        "400":
          description: "Invalid service type ID or status change not allowed"
        "404":
          description: "Sub service not found"
        "500":
          description: "Internal Server Error"

    delete:
      tags:
        - "SubService"
      summary: "Delete a Sub service"
      description: "Softdelete a Sub service from the system using their ID."
      operationId: "deleteSubService"
      produces:
        - "application/json"
      parameters:
        - in: "path"
          name: "id"
          schema:
            type: string
          required: true
          description: "Unique identifier of the Sub service"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Sub Service deleted successfully."
        "400":
          description: "Invalid Service type by ID."
        "500":
          description: "Internal server error."

components:
  schemas:
    createSubService:
      type: object
      required:
        - serviceTypeId
        - name
      properties:
        serviceTypeId:
          type: string
          format: uuid
          description: "Enter the serviceTypeId of the service type"
        name:
          type: string
          description: "Enter the name of the Sub Service"
        description:
          type: string
          description: "Enter the description of the Sub Service"
        imageUrl:
          type: string
          description: "Enter the imageUrl of the Sub Service"
        serviceOptions:
          type: array
          description: "List of service options"
          items:
            type: object
            required:
              - name
              - serviceIntervalType
            properties:
              name:
                type: string
                description: "Name of the service option"
              serviceIntervalType:
                type: string
                enum: [days, miles]
                description: "Service interval type (days or miles)"
              serviceIntervalValue:
                type: string
                description: "Service interval value"

    updateSubService:
      type: object
      properties:
        serviceTypeId:
          type: string
          format: uuid
          description: "Enter the serviceTypeId of the service type"
        name:
          type: string
          description: "Enter the name of the Sub Service"
        description:
          type: string
          description: "Enter the description of the Sub Service"
        imageUrl:
          type: string
          description: "Enter the imageUrl of the Sub Service"