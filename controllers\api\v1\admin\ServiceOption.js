const ServiceOptionRepository = require('../../../../models/repositories/ServiceOptionRepository');
const {
  genRes,
  errorMessage,
  resCode,
  errorTypes,
} = require('../../../../config/options');

exports.postServiceOption = async (req, res) => {
  try {
    const { subServiceId } = req.query;

    const { success, message, data } =
      await ServiceOptionRepository.checkAndCreateOrUpdateServiceOptionInBulk(
        subServiceId,
        req.body
      );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.putServiceOption = async (req, res) => {
  try {
    const serviceOptionId = req.params.id;

    const { success, message, data } =
      await ServiceOptionRepository.checkANdUpdateSingleServiceOption(
        serviceOptionId,
        req.body
      );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.getServiceOptionList = async (req, res) => {
  try {
    const {
      serviceTypeId,
      subServiceId,
      start = 0,
      limit = 10,
      search = null,
    } = req.query;

    const { message, data } =
      await ServiceOptionRepository.getServiceOptionAndCount({
        serviceTypeId,
        subServiceId,
        start: parseInt(start),
        limit: parseInt(limit),
        search,
      });

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.patchChangeStatusServiceOption = async (req, res) => {
  try {
    const { success, message } =
      await ServiceOptionRepository.checkAndPatchServiceOptionStatus(
        req.params.id,
        false
      );
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(
            resCode.HTTP_BAD_REQUEST,
            message,
            errorTypes.ACCESS_DENIED_EXCEPTION
          )
        );
    }
    return res
      .status(resCode.HTTP_CREATE)
      .json(genRes(resCode.HTTP_CREATE, { message }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};

exports.deleteServiceOption = async (req, res) => {
  try {
    const { success, message } =
      await ServiceOptionRepository.checkAndPatchServiceOptionStatus(
        req.params.id,
        true
      );
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(
            resCode.HTTP_BAD_REQUEST,
            message,
            errorTypes.ACCESS_DENIED_EXCEPTION
          )
        );
    }
    return res
      .status(resCode.HTTP_CREATE)
      .json(genRes(resCode.HTTP_CREATE, { message }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};
