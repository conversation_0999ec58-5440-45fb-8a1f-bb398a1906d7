'use strict';

const { serviceRequestStatus } = require('../config/options');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn(
      'ServiceRequestProvider',
      'requestStatus',
      {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: serviceRequestStatus.PENDING,
      }
    );
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn(
      'ServiceRequestProvider',
      'requestStatus',
      {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: serviceRequestStatus.AVAILABLE,
      }
    );
  },
};
