const sequelize = require('sequelize');
const { User } = require('..');
const {
  userStatus,
  errorMessage,
  successMessage,
  emailTypes,
  genOtp,
  otpExpireInMins,
} = require('../../config/options');
const UserHelper = require('../helpers/UserHelper');
const { generatePassword } = require('../helpers/UtilHelper');
const { sendResetPasswordOTPEmail } = require('./EmailRepository');
const { sendMobileOtp } = require('./SMSRepository');
const { Op } = sequelize;

const findByCondition = async (conditions, options) =>
  await User.findOne({ where: conditions, ...options });

const getUserAndCount = async ({
  start = 0,
  limit = 10,
  search = '',
  status,
  fromDate,
  toDate,
  role,
}) => {
  try {
    const where = {
      status: {
        [Op.in]: Array.isArray(status)
          ? status
          : status
            ? status.split(',')
            : [userStatus.ACTIVE],
      },
      role,
    };

    if (search && !['all', 'null', 'undefined'].includes(search)) {
      where[Op.or] = [
        { firstName: { [Op.iLike]: `%${search}%` } },
        { lastName: { [Op.iLike]: `%${search}%` } },
      ];
    }

    if (fromDate || toDate) {
      where.creationDate = {};
      if (fromDate) where.creationDate[Op.gte] = fromDate;
      if (toDate) where.creationDate[Op.lte] = toDate;
    }

    const { count, rows } = await User.findAndCountAll({
      where,
      offset: Number(start),
      limit: Number(limit),
      order: [['createdAt', 'DESC']],
      attributes: [
        'id',
        'firstName',
        'lastName',
        'email',
        'profilePicture',
        'countryCode',
        'mobileNumber',
        'status',
        'createdAt',
        'lastSignInAt',
        [sequelize.literal(`CONCAT("firstName", ' ', "lastName")`), 'fullName'],
      ],
    });

    return {
      message: successMessage.DETAIL_MESSAGE('User'),
      data: {
        rows,
        pagination: {
          totalCount: count,
          start: Number(start),
          limit: Number(limit),
        },
      },
    };
  } catch (error) {
    throw new Error(`Error fetching users: ${error.message}`);
  }
};

const getUser = async (id) => {
  try {
    const existingUser = await User.findOne({
      where: { id, status: { [Op.not]: userStatus.DELETED } },
      attributes: [
        'id',
        'firstName',
        'lastName',
        'email',
        'profilePicture',
        'status',
        'role',
        'createdAt',
        'lastSignInAt',
      ],
    });

    if (!existingUser) {
      return {
        success: false,
        message: errorMessage.NO_USER('email address'),
      };
    } else if (existingUser.status === userStatus.BLOCKED) {
      return {
        success: false,
        message: errorMessage.USER_ACCOUNT_BLOCKED,
      };
    } else {
      existingUser.lastSignInAt = new Date();
      await existingUser.save();
      const data = {
        ...UserHelper.modifyOutputData(existingUser),
        token: existingUser.genToken(),
      };
      return {
        success: true,
        message: successMessage.LOG('logged in'),
        data,
      };
    }
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndLoginWithPasswordWithRole = async (body, role) => {
  try {
    const existingUser = await User.findOne({
      where: {
        email: body.email,
        role,
        status: { [Op.ne]: userStatus.DELETED },
      },
    });

    if (!existingUser) {
      return {
        success: false,
        message: errorMessage.NO_USER('email address'),
      };
    } else if (!existingUser.validPassword(body.password)) {
      return {
        success: false,
        message: errorMessage.INVALID_CREDENTIALS,
      };
    } else if (existingUser.status === userStatus.BLOCKED) {
      return {
        success: false,
        message: errorMessage.USER_ACCOUNT_BLOCKED,
      };
    } else {
      existingUser.lastSignInAt = new Date();
      await existingUser.save();
      const data = {
        ...UserHelper.modifyOutputData(existingUser),
        token: existingUser.genToken(),
      };
      return {
        success: true,
        message: successMessage.LOG('logged in'),
        data,
      };
    }
  } catch (error) {
    throw new Error(`Error Admin logging in: ${error.message}`);
  }
};

const checkDuplicate = async (body, existingUser) => {
  try {
    const conditions = [];

    if (body.email) {
      conditions.push({ email: body.email });
    }
    if (body.mobileNumber) {
      conditions.push({
        mobileNumber: body.mobileNumber,
        countryCode: body.countryCode,
      });
    }

    if (conditions.length === 0) {
      return null;
    }
    return await User.findOne({
      where: {
        [Op.or]: conditions,
        id: { [Op.ne]: existingUser.id },
        status: { [Op.ne]: userStatus.DELETED },
      },
      attributes: ['id', 'email', 'mobileNumber', 'countryCode'],
    });
  } catch (error) {
    throw new Error(`Error revilo duplicate user: ${error.message}`);
  }
};

const createUser = async (data) => {
  try {
    const password = data.password && (await generatePassword(data.password));
    const payload = {
      password,
      firstName: data.firstName,
      lastName: data.lastName,
      countryCode: data.countryCode,
      mobileNumber: data.mobileNumber,
      role: data.role,
      profilePicture: data.profilePicture || null,
      email: data.email,
      googleId: data.googleId || null,
      appleId: data.appleId || null,
      facebookId: data.facebookId || null,
    };
    return await User.create(payload);
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndCreate = async (body) => {
  try {
    const query = {
      status: { [Op.ne]: userStatus.DELETED },
      [Op.or]: [
        body.email && { email: body.email },
        body.mobileNumber && {
          mobileNumber: body.mobileNumber,
          countryCode: body.countryCode,
        },
        body.googleId && {
          googleId: body.googleId,
        },
        body.appleId && {
          appleId: body.appleId,
        },
        body.facebookId && {
          facebookId: body.facebookId,
        },
      ].filter(Boolean),
      ...(body.role && { role: body.role }),
    };

    const existingUser = await User.findOne({
      where: query,
      attributes: [
        'id',
        'countryCode',
        'mobileNumber',
        'email',
        'status',
        'role',
        'profilePicture',
        'googleId',
        'appleId',
        'facebookId',
      ],
    });

    if (!existingUser) {
      const data = await createUser(body);
      const message = successMessage.ADD_SUCCESS_MESSAGE('User');
      return {
        success: true,
        message,
        data: {
          ...UserHelper.modifyOutputData(data),
          isNew: true,
        },
      };
    }
    if (existingUser.status === userStatus.BLOCKED) {
      return {
        success: false,
        message: errorMessage.USER_ACCOUNT_BLOCKED,
      };
    }
    if (
      existingUser &&
      (existingUser.googleId || existingUser.appleId || existingUser.facebookId)
    ) {
      return {
        success: true,
        data: {
          ...UserHelper.modifyOutputData(existingUser),
          token: existingUser.genToken(),
          isNew: false,
        },
      };
    }
    return {
      success: false,
      message: errorMessage.EXISTS_USER('email or phone number'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndUpdateUser = async (query, data) => {
  try {
    const existingUser = await User.findOne(query);
    if (!existingUser) {
      return { success: false, message: errorMessage.DOES_NOT_EXIST('User') };
    }

    if (data.email && data.email !== existingUser.email) {
      const emailExists = await User.findOne({
        where: {
          email: data.email,
          role: existingUser.role,
          id: { [Op.ne]: existingUser.id },
          status: { [Op.notIn]: [userStatus.DELETED] },
        },
      });

      if (emailExists) {
        return {
          success: false,
          message: errorMessage.ALREADY_EXIST('Email'),
        };
      }

      existingUser.email = data.email;
    }

    existingUser.firstName = data.firstName;
    existingUser.lastName = data.lastName;
    existingUser.profilePicture = data.profilePicture;

    await existingUser.save();
    return {
      success: true,
      data: existingUser,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('User'),
    };
  } catch (e) {
    throw new Error(e);
  }
};

const checkAndUpdateStatus = async (id, isDelete) => {
  try {
    const query = {
      id,
      status: { [Op.ne]: userStatus.DELETED },
    };

    const existingUser = await User.findOne({
      where: query,
      attributes: ['id', 'email', 'mobileNumber', 'countryCode', 'status'],
    });

    if (!existingUser) {
      return { success: false, message: errorMessage.DOES_NOT_EXIST('User') };
    }

    existingUser.status =
      existingUser.status === userStatus.ACTIVE
        ? userStatus.BLOCKED
        : userStatus.ACTIVE;

    if (isDelete) {
      existingUser.status = userStatus.DELETED;
      existingUser.mobileNumber = `${existingUser.mobileNumber}${Date.now()}${userStatus.DELETED}`;
      existingUser.email = `${existingUser.email}${Date.now()}${userStatus.DELETED}`;
    }

    await existingUser.save();

    return {
      success: true,
      message: successMessage.CHANGED_SUCCESS_MESSAGE('User status'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

const generateAndSendOtp = async (
  existingUser,
  isEmail = false,
  emailType = null
) => {
  const todayDate = new Date();
  let tempOtp = genOtp();
  todayDate.setMinutes(todayDate.getMinutes() + otpExpireInMins);
  await User.update(
    {
      tempOtp,
      tempOtpExpiresAt: todayDate,
    },
    {
      where: {
        id: existingUser.id,
      },
    }
  );
  if (!isEmail) {
    // send to mobile
    sendMobileOtp(existingUser, tempOtp);
  } else {
    const payload = {
      id: existingUser.id,
      firstName: existingUser.firstName,
      lastName: existingUser.lastName,
      email: existingUser.email,
      tempOtp,
    };
    if (emailTypes.EMAIL_RESET_PASSWORD_OTP === emailType) {
      sendResetPasswordOTPEmail(payload);
    }
  }
  return true;
};

const sendPasswordResetOtp = async (data) => {
  try {
    const query = {
      email: data.email,
      status: { [Op.ne]: userStatus.DELETED },
    };
    const existingUser = await User.findOne({
      where: query,
      attributes: [
        'id',
        'email',
        'mobileNumber',
        'status',
        'tempOtp',
        'tempOtpExpiresAt',
      ],
    });

    if (!existingUser) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('User'),
      };
    }
    if (existingUser && existingUser.status === userStatus.BLOCKED) {
      return {
        success: false,
        message: errorMessage.USER_ACCOUNT_BLOCKED,
      };
    }

    await generateAndSendOtp(
      existingUser,
      data.type === 'email',
      emailTypes.EMAIL_RESET_PASSWORD_OTP
    );

    return {
      success: true,
      message: successMessage.OTP_SEND(
        data.type === 'email' ? 'email address' : 'registered mobile number'
      ),
    };
  } catch (e) {
    throw new Error(e);
  }
};

const checkOtpAndUpdatePassword = async (body) => {
  try {
    const query = {
      email: body.email,
      status: { [Op.ne]: userStatus.DELETED },
      tempOtp: body.tempOtp,
      tempOtpExpiresAt: { [Op.gte]: new Date() },
    };

    const existingUser = await User.findOne({
      where: query,
    });

    if (!existingUser) {
      return {
        success: false,
        message: errorMessage.OTP_INVALID,
        data: null,
      };
    }
    existingUser.tempOtp = null;
    existingUser.tempOtpExpiresAt = null;
    existingUser.password = body.password;

    await existingUser.save();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Password'),
    };
  } catch (e) {
    throw new Error(e);
  }
};

const checkAndVerifyOtp = async (body) => {
  try {
    const whereClause = {
      status: { [Op.notIn]: [userStatus.DELETED] },
      tempOtp: body.tempOtp,
      tempOtpExpiresAt: { [Op.gte]: new Date() },
    };

    if (body.type === 'email') {
      whereClause.email = body.email;
    } else {
      whereClause.mobileNumber = body.mobileNumber;
      whereClause.countryCode = body.countryCode;
      whereClause.role = body.role;
    }

    const existingUser = await User.findOne({ where: whereClause });

    if (!existingUser) {
      return {
        success: false,
        message: errorMessage.OTP_INVALID,
        data: null,
      };
    }

    existingUser.tempOtp = null;
    existingUser.tempOtpExpiresAt = null;
    existingUser.lastSignInAt = new Date();
    existingUser.status = userStatus.ACTIVE;

    await existingUser.save();

    const data = {
      ...UserHelper.modifyOutputData(existingUser),
      token: existingUser.genToken(),
    };

    return {
      success: true,
      message: successMessage.OTP_VERIFIED(),
      data,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || 'Something went wrong',
      data: null,
    };
  }
};

const signupWithPhone = async (body) => {
  const { email, countryCode, mobileNumber } = body;

  try {
    if (email) {
      const existingUser = await User.findOne({ where: { email } });

      if (existingUser) {
        existingUser.mobileNumber = mobileNumber;
        existingUser.countryCode = countryCode;
        await existingUser.save();

        await generateAndSendOtp(existingUser, false);

        return {
          success: true,
          message: successMessage.OTP_SEND('Mobile Number'),
        };
      }
    }

    const response = await checkAndCreate(body);

    if (!response.success) {
      return {
        success: false,
        message: response.message,
      };
    }

    const user = response.data;
    await generateAndSendOtp(user, false);

    return {
      success: true,
      message: successMessage.OTP_SEND('Mobile Number'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndSendOTPForLogin = async (body) => {
  try {
    let whereClause = {
      status: { [Op.notIn]: [userStatus.DELETED] },
    };

    if (body.type === 'email') {
      whereClause.email = body.email;
    } else {
      whereClause.mobileNumber = body.mobileNumber;
      whereClause.countryCode = body.countryCode;
      whereClause.role = body.role;
    }

    const existingUser = await User.findOne({ where: whereClause });

    if (!existingUser) {
      return {
        success: false,
        message: errorMessage.NO_USER('Mobile Number'),
      };
    }

    await generateAndSendOtp(
      existingUser,
      body.type === 'email',
      body.emailType
    );

    return {
      success: true,
      message: successMessage.OTP_SEND(
        body.type === 'email' ? 'Email' : 'Mobile number'
      ),
    };
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndSendOTPForChangeEmailAndMobile = async (body, userId) => {
  try {
    let whereClause = {
      status: { [Op.notIn]: [userStatus.DELETED] },
      id: { [Op.not]: userId },
    };

    if (body.type === 'email') {
      whereClause.email = body.email;
    } else {
      whereClause.mobileNumber = body.mobileNumber;
      whereClause.countryCode = body.countryCode;
      whereClause.role = body.role;
    }

    const userExitingWithData = await User.findOne({ where: whereClause });

    if (userExitingWithData) {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST(
          body.type === 'email' ? 'email address' : 'Mobile Number'
        ),
      };
    }
    const loggedInUser = await User.findOne({
      where: {
        id: userId,
      },
      attributes: ['id', 'email', 'mobileNumber', 'countryCode'],
    });

    await generateAndSendOtp(
      loggedInUser,
      body.type === 'email',
      body.emailType
    );

    return {
      success: true,
      message: successMessage.OTP_SEND(
        body.type === 'email' ? 'Email' : 'Mobile number'
      ),
    };
  } catch (error) {
    throw new Error(error);
  }
};
const checkAndVerifyOtpToChangeEmailAndMobile = async (body, userId) => {
  try {
    const whereClause = {
      status: { [Op.notIn]: [userStatus.DELETED] },
      tempOtp: body.tempOtp,
      tempOtpExpiresAt: { [Op.gte]: new Date() },
      id: userId,
    };
    const existingUser = await User.findOne({
      where: whereClause,
      attributes: [
        'id',
        'email',
        'mobileNumber',
        'countryCode',
        'tempOtp',
        'tempOtpExpiresAt',
        'profilePicture',
        'role',
        'firstName',
        'lastName',
        'googleId',
        'facebookId',
        'appleId',
      ],
    });

    if (!existingUser) {
      return {
        success: false,
        message: errorMessage.OTP_INVALID,
        data: null,
      };
    }

    existingUser.tempOtp = null;
    existingUser.tempOtpExpiresAt = null;
    if (body.type === 'email') {
      existingUser.email = body.email;
    } else {
      existingUser.mobileNumber = body.mobileNumber;
      existingUser.countryCode = body.countryCode;
      existingUser.role = body.role;
    }

    await existingUser.save();

    const data = {
      ...UserHelper.modifyOutputData(existingUser),
    };

    return {
      success: true,
      message: successMessage.OTP_VERIFIED(),
      data,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || 'Something went wrong',
      data: null,
    };
  }
};
const checkAndUpdateEmail = async (user, body) => {
  try {
    const existingUser = await User.findOne({
      where: {
        email: body.email,
        id: { [Op.ne]: user.id },
      },
      attributes: ['id'],
    });

    if (existingUser) {
      return {
        success: false,
        message: errorMessage.EXISTS_USER('Email'),
        data: null,
      };
    }

    user.email = body.email;
    await user.save();

    const limitedData = {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      status: user.status,
      mobileNumber: user.mobileNumber,
      email: user.email,
    };

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('email'),
      data: limitedData,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || 'Something went wrong',
      data: null,
    };
  }
};

const getUserProfile = async (id) => {
  try {
    const user = await User.findOne({
      where: {
        id,
        status: { [Op.not]: userStatus.DELETED },
      },
      attributes: UserHelper.userAttributes(),
    });

    if (!user) {
      return {
        success: false,
        message: errorMessage.NO_USER('user'),
      };
    }

    if (user.status === userStatus.BLOCKED) {
      return {
        success: false,
        message: errorMessage.USER_ACCOUNT_BLOCKED,
      };
    }

    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE('User profile'),
      data: user,
    };
  } catch (error) {
    console.error(error);
    throw new Error(error);
  }
};

module.exports = {
  findByCondition,
  getUserAndCount,
  getUser,
  checkAndLoginWithPasswordWithRole,
  checkDuplicate,
  createUser,
  checkAndCreate,
  checkAndUpdateUser,
  generateAndSendOtp,
  sendPasswordResetOtp,
  checkOtpAndUpdatePassword,
  checkAndUpdateStatus,
  checkAndVerifyOtp,
  checkAndSendOTPForChangeEmailAndMobile,
  checkAndVerifyOtpToChangeEmailAndMobile,
  signupWithPhone,
  checkAndSendOTPForLogin,
  checkAndUpdateEmail,
  getUserProfile,
};
