const { complianceLegalTitle } = require('../../config/options');

const createComplianceLegal = {
  title: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'title cannot be empty',
    },
    isString: {
      errorMessage: 'title must be a string',
    },
    isIn: {
      options: [
        [
          complianceLegalTitle.TERMS_AND_CONDITIONS,
          complianceLegalTitle.PRIVACY_POLICY,
        ],
      ],
      errorMessage: `title must be ${(complianceLegalTitle.TERMS_AND_CONDITIONS, complianceLegalTitle.PRIVACY_POLICY)}`,
    },
  },
  description: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'description cannot be empty',
    },
    isString: {
      errorMessage: 'description must be a string',
    },
  },
};

const updateComplianceLegal = {
  description: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'description cannot be empty',
    },
    isString: {
      errorMessage: 'description must be a string',
    },
  },
};

module.exports = {
  createComplianceLegal,
  updateComplianceLegal,
};
