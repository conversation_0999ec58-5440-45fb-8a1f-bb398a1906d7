const { Router } = require('express');
const router = Router();
const {
  postComplianceLegal,
  getComplianceLegalListing,
  getComplianceLegalById,
  putComplianceLegal,
} = require('../../../../controllers/api/v1/admin/ComplianceLegal');
const { checkSchema } = require('express-validator');

const ErrorHandleHelper = require('../../../../models/helpers/ErrorHandleHelper');
const {
  createComplianceLegal,
  updateComplianceLegal,
} = require('../../../../schema-validation/admin/ComplianceLegal');

router.post(
  '/',
  checkSchema(createComplianceLegal),
  ErrorHandleHelper.requestValidator,
  postComplianceLegal
);

router.get('/', getComplianceLegalListing);

router.get('/:id', getComplianceLegalById);

router.put(
  '/:id',
  checkSchema(updateComplianceLegal),
  ErrorHandleHelper.requestValidator,
  putComplianceLegal
);

module.exports = router;
