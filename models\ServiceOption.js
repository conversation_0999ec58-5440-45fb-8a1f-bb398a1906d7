'use strict';

const { serviceStatus, serviceInterval } = require('../config/options');

const { Op } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  const ServiceOption = sequelize.define(
    'ServiceOption',
    {
      id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      status: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: serviceStatus.ACTIVE,
      },
      serviceIntervalType: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: serviceInterval.MILES,
      },
      serviceIntervalValue: {
        type: DataTypes.STRING,
        allowNull: true,
      },
    },
    {
      tableName: 'ServiceOption',
      timestamps: true,
      defaultScope: {
        where: {
          status: {
            [Op.ne]: serviceStatus.DELETED,
          },
        },
      },

      scopes: {
        withDeleted: {
          // No conditions here to include all records including deleted
        },
        onlyDeleted: {
          where: {
            status: serviceStatus.DELETED,
          },
        },
      },
    }
  );

  ServiceOption.associate = function (models) {
    ServiceOption.belongsTo(models.SubService, {
      foreignKey: 'subServiceId',
      as: 'subService',
    });
  };

  return ServiceOption;
};
