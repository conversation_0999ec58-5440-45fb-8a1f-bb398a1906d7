const { Router } = require('express');

const router = Router();
const { checkSchema } = require('express-validator');

const {
  postServiceCategory,
  putServiceCategory,
  getServiceCategoryListing,
  patchChangeStatusServiceCategory,
  deleteServiceCategory,
} = require('../../../../controllers/api/v1/admin/ServiceCategory');

const {
  requestValidator,
} = require('../../../../models/helpers/ErrorHandleHelper');
const {
  createServiceCategory,
  updateServiceCategory,
} = require('../../../../schema-validation/admin/ServiceCategory');

router.post(
  '/',
  checkSchema(createServiceCategory),
  requestValidator,
  postServiceCategory
);

router.put(
  '/:id',
  checkSchema(updateServiceCategory),
  requestValidator,
  putServiceCategory
);

router.get('/', getServiceCategoryListing);

router.patch('/:id', patchChangeStatusServiceCategory);

router.delete('/:id', deleteServiceCategory);

module.exports = router;
