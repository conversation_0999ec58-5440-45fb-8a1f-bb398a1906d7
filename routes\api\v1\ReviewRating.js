const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();

const ReviewRatingControl = require('../../../controllers/api/v1/ReviewRating');
const ReviewRatingSchema = require('../../../schema-validation/ReviewRating');
const ErrorHandleHelper = require('../../../models/helpers/ErrorHandleHelper');

router.post(
  '/',
  checkSchema(ReviewRatingSchema.createRatingAndReview),
  ErrorHandleHelper.requestValidator,
  ReviewRatingControl.postReviewRating
);

router.get('/', ReviewRatingControl.getReviewRatingListing);

router.get('/statistics', ReviewRatingControl.getReviewRatingStats);

module.exports = router;
