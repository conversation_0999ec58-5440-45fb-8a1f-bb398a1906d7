const { addressType } = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const Address = sequelize.define(
    'Address',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      userId: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      zipcode: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      location: {
        type: DataTypes.GEOGRAPHY('POINT', 4326),
        allowNull: true,
      },
      radius: {
        type: DataTypes.DECIMAL,
        allowNull: true,
      },
      city: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      state: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      addressType: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: addressType.HOME,
      },
      addressLine1: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      category: {
        type: DataTypes.STRING,
        allowNull: true,
      },
    },
    {
      tableName: 'Address',
      timestamps: true,
    }
  );

  Address.associate = function (models) {
    Address.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
    });
  };

  return Address;
};
