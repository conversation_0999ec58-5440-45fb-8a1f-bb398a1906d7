const createAndUpdateServiceType = {
  categoryId: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'categoryId can not be empty',
    isUUID: {
      errorMessage: 'categoryId must be a valid UUID',
    },
  },
  name: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'name cannot be empty',
    isString: {
      errorMessage: 'name must be string',
    },
  },
  description: {
    in: ['body'],
    trim: true,
    notEmpty: false,
    isString: {
      errorMessage: 'description  must be string',
    },
  },
  imageUrl: {
    in: ['body'],
    trim: true,
    notEmpty: false,
    isString: {
      errorMessage: 'imageUrl  must be string',
    },
  },
};

module.exports = {
  createAndUpdateServiceType,
};
