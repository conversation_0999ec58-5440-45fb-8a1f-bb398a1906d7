const { ComplianceLegal, User } = require('..');
const { Op } = require('sequelize');
const { successMessage, errorMessage } = require('../../config/options');

const checkAndCreateComplianceLegal = async (userId, body) => {
  try {
    const existingComplianceLegal = await ComplianceLegal.findOne({
      where: {
        title: body.title.trim(),
      },
    });

    if (existingComplianceLegal) {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST('Compliance and Legal'),
      };
    }

    const complianceLegal = await ComplianceLegal.create({
      ...body,
      createdBy: userId,
    });

    return {
      success: true,
      message: successMessage.SAVED_SUCCESS_MESSAGE('Compliance and Legal'),
      data: complianceLegal,
    };
  } catch (error) {
    throw new Error(error);
  }
};

const getComplianceLegalCount = async ({ start = 0, limit = 10, search }) => {
  try {
    const where = {
      ...(search && {
        [Op.or]: [{ title: { [Op.iLike]: `%${search}%` } }],
      }),
    };

    const { count, rows } = await ComplianceLegal.findAndCountAll({
      where,
      offset: Number(start),
      limit: Number(limit),
      order: [['createdAt', 'DESC']],
      attributes: ['id', 'title', 'updatedAt', 'createdAt'],
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'firstName', 'lastName'],
        },
      ],
    });

    return {
      message: successMessage.DETAIL_MESSAGE('Compliance and Legal'),
      data: {
        rows,
        pagination: {
          totalCount: count,
          start: Number(start),
          limit: Number(limit),
        },
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

const checkAndGetComplianceLegal = async (id) => {
  const existingComplianceLegal = await ComplianceLegal.findOne({
    where: {
      id,
    },
    attributes: ['id', 'title', 'description', 'updatedAt', 'createdAt'],
    include: [
      {
        model: User,
        as: 'user',
        attributes: ['id', 'firstName', 'lastName'],
      },
    ],
  });

  if (!existingComplianceLegal) {
    return {
      success: false,
      message: errorMessage.DOES_NOT_EXIST('Compliance and Legal'),
    };
  }

  return {
    success: true,
    data: existingComplianceLegal,
    message: successMessage.DETAIL_MESSAGE('Compliance and Legal'),
  };
};

const checkAndUpdateComplianceLegal = async (complianceId, body) => {
  try {
    const existingComplianceLegal = await ComplianceLegal.findOne({
      where: {
        id: complianceId,
      },
    });

    if (!existingComplianceLegal) {
      return {
        success: false,
        message: errorMessage.NOT_FOUND('Compliance and Legal'),
      };
    }

    existingComplianceLegal.description = body.description;

    await existingComplianceLegal.save();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Compliance and Legal'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

module.exports = {
  checkAndCreateComplianceLegal,
  getComplianceLegalCount,
  checkAndGetComplianceLegal,
  checkAndUpdateComplianceLegal,
};
