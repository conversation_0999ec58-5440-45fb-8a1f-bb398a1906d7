exports.createOrUpdateVehicle = {
  vehicleType: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Vehicle type is required',
    },
    isString: {
      errorMessage: 'Vehicle type must be a string',
    },
  },
  year: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Year must be a valid integer',
    },
    toInt: true,
  },
  industry: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'Industry must be a string',
    },
  },
  model: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'Model must be a string',
    },
  },
  vehicleTrim: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'Vehicle trim must be a string',
    },
  },
  engine: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'Engine must be a string',
    },
  },
  driveTrain: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'Drivetrain must be a string',
    },
  },
  milage: {
    in: ['body'],
    optional: true,
    isInt: {
      options: { min: 0 },
      errorMessage: 'Milage must be a non-negative integer',
    },
    toInt: true,
  },
  vin: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'VIN must be a string',
    },
  },
  plateNumber: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'Plate number must be a string',
    },
  },
  state: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'State must be a string',
    },
  },
};

exports.createVehicleFromVin = {
  vin: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'VIN is required',
    },
    isString: {
      errorMessage: 'VIN must be a string',
    },
  },
};
