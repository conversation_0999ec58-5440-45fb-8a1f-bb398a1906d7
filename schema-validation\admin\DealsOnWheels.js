const { dealsOnWheelsStatus } = require('../../config/options');

const createOrUpdateDealsOnWheelsSchema = {
  posterImage: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'posterImage is required',
    },
    isString: {
      errorMessage: 'posterImage must be a string',
    },
  },
  redirectionUrl: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'redirectionUrl is required',
    },
    isString: {
      errorMessage: 'redirectionUrl must be a string',
    },
  },
};

const updateDealsOnWheelsStatusSchema = {
  status: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'status must be a string',
    },
    isIn: {
      options: [Object.values(dealsOnWheelsStatus)],
      errorMessage: `status must be one of: ${Object.values(dealsOnWheelsStatus).join(', ')}`,
    },
  },
};

module.exports = {
  createOrUpdateDealsOnWheelsSchema,
  updateDealsOnWheelsStatusSchema,
};
