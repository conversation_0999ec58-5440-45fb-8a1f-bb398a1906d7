paths:
  /admin/service-option:
    post:
      tags:
        - "ServiceOption"
      summary: "Create a new Service Option"
      description: "Add a new Service Option under a specific Sub Service and Service Type."
      operationId: "createServiceOption"
      parameters:
        - name: subServiceId
          in: query
          required: true
          schema:
            type: string
            format: uuid
          description: "UUID of the Sub Service to which this Service Option belongs."
      requestBody:
        description: "Payload containing Sub service details."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/createServiceOption"
        required: true
      produces:
        - application/json
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Service Option created successfully."
        "400":
          description: "Invalid request payload."
        "409":
          description: "Service Option with the same name already exists."
        "500":
          description: "Internal server error."

    get:
      tags:
        - "ServiceOption"
      summary: "Get list of Service option with pagination and search"
      description: "Fetch paginated list of Service option with optional search filter."
      operationId: "listServiceOption"
      parameters:
        - name: "serviceTypeId"
          in: "query"
          required: true
          description: "Service Type ID"
          schema:
            type: string
            format: uuid
        - name: "subServiceId"
          in: "query"
          required: true
          description: "Sub service ID"
          schema:
            type: string
            format: uuid
        - name: "start"
          in: "query"
          description: "Starting index for pagination"
          required: false
          schema:
            type: integer
            default: 0
        - name: "limit"
          in: "query"
          description: "Number of records to return"
          required: false
          schema:
            type: integer
            default: 10
        - name: "search"
          in: "query"
          description: "Search keyword to filter types by name"
          required: false
          schema:
            type: string
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "List of Service option fetched successfully."
        "400":
          description: "Invalid request parameters."
        "500":
          description: "Internal server error."

  /admin/service-option/{id}:
    put:
      tags:
        - "ServiceOption"
      summary: "Update an existing Service Option"
      description: "Update the name or subServiceId of a Service Option."
      operationId: "updateServiceOption"
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: "UUID of the Service Option to update."
      requestBody:
        description: "Payload with updated Service Option details."
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/updateServiceOption"
        required: true
      produces:
        - application/json
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Service Option updated successfully."
        "400":
          description: "Invalid request payload or duplicate name."
        "404":
          description: "Service Option not found."
        "500":
          description: "Internal server error."

    patch:
      tags:
        - "ServiceOption"
      summary: "Update Service Option status by ID"
      description: "update a Service Option status using its ID"
      operationId: "updateServiceOptionStatus"
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: "ID of the  Service Option to update status"
          schema:
            type: string
      responses:
        "201":
          description: "Service Option status updated successfully"
        "400":
          description: "Invalid service type ID or status change not allowed"
        "404":
          description: "Service Option not found"
        "500":
          description: "Internal Server Error"

    delete:
      tags:
        - "ServiceOption"
      summary: "Delete a Service Option"
      description: "Softdelete a Service Option from the system using their ID."
      operationId: "deleteServiceOption"
      produces:
        - "application/json"
      parameters:
        - in: "path"
          name: "id"
          schema:
            type: string
          required: true
          description: "Unique identifier of the Service Option"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Service Option deleted successfully."
        "400":
          description: "Invalid Service type by ID."
        "500":
          description: "Internal server error."

components:
  schemas:
    createServiceOption:
      type: object
      properties:
        options:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
                description: "Enter the name of the Service Option"
              serviceIntervalType:
                type: string
                enum: ["miles", "days"] 
                description: "Type of service interval"
              serviceIntervalValue:
                type: string
                description: "Value for the service interval"
            required:
              - name
      required:
        - options

    updateServiceOption:
      type: object
      required:
        - name
        - subServiceId
      properties:
        name:
          type: string
          example: "Option A"
        subServiceId:
          type: string
          format: uuid
          example: "f5d9b4e2-dfc6-4a21-bbd8-b65f87a8d1e6"
        serviceIntervalType:
          type: string
          enum: ["miles", "days"] 
          example: "miles"
          description: "Type of service interval"
        serviceIntervalValue:
          type: string
          example: "5000"
          description: "Value of the service interval (e.g., 5000 miles)"