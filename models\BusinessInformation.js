const { generateCloudFrontUrl } = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const BusinessInformation = sequelize.define(
    'BusinessInformation',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      businessLogo: {
        allowNull: true,
        type: DataTypes.TEXT,
        get() {
          return generateCloudFrontUrl(this.getDataValue('businessLogo'));
        },
        set(file) {
          if (file) {
            this.setDataValue(
              'businessLogo',
              `uploads/${file.split('uploads/')[1]}`
            );
          }
        },
      },
      businessName: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      yearOfExperience: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      hasCertificationLicense: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
      },
      isInsured: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
      },
      hasToolset: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
      },
      about: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
    },
    {
      tableName: 'BusinessInformation',
      timestamps: true,
    }
  );

  BusinessInformation.associate = function (models) {
    BusinessInformation.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE',
    });

    BusinessInformation.hasMany(models.BusinessDocument, {
      foreignKey: 'businessId',
      as: 'documents',
      onDelete: 'CASCADE',
    });
  };

  return BusinessInformation;
};
