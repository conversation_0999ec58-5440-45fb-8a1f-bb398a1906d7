const { dayOfWeek } = require('../config/options');

exports.validateProvidedService = {
  note: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'Note must be a string',
    },
  },

  serviceTypes: {
    in: ['body'],
    optional: true,
    isArray: {
      errorMessage: 'serviceTypes must be an array',
    },
  },

  'serviceTypes.*.serviceTypeId': {
    in: ['body'],
    notEmpty: {
      errorMessage: 'serviceTypeId is required',
    },
    isUUID: {
      errorMessage: 'serviceTypeId must be a valid UUID',
    },
  },

  'serviceTypes.*.minPrice': {
    in: ['body'],
    optional: true,
    isDecimal: {
      errorMessage: 'minPrice must be a decimal number',
    },
  },

  'serviceTypes.*.maxPrice': {
    in: ['body'],
    optional: true,
    isDecimal: {
      errorMessage: 'maxPrice must be a decimal number',
    },
  },

  'cancellationPolicy.startTime': {
    in: ['body'],
    optional: true,
    matches: {
      options: [/^([01]\d|2[0-3]):([0-5]\d):([0-5]\d)$/],
      errorMessage: 'startTime must be a valid time (HH:mm:ss)',
    },
  },

  'cancellationPolicy.endTime': {
    in: ['body'],
    optional: true,
    matches: {
      options: [/^([01]\d|2[0-3]):([0-5]\d):([0-5]\d)$/],
      errorMessage: 'endTime must be a valid time (HH:mm:ss)',
    },
  },

  'cancellationPolicy.cancellationFee': {
    in: ['body'],
    optional: true,
    isDecimal: {
      errorMessage: 'cancellationFee must be a decimal number',
    },
  },

  availability: {
    in: ['body'],
    optional: true,
    isArray: {
      errorMessage: 'availability must be an array',
    },
  },

  'availability.*.dayOfWeek': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'dayOfWeek must be a string',
    },
    isIn: {
      options: [Object.values(dayOfWeek)],
      errorMessage: `Day of Week must be one of: ${Object.values(dayOfWeek).join(', ')}`,
    },
  },

  'availability.*.slots': {
    in: ['body'],
    optional: true,
    isArray: {
      errorMessage: 'slots must be an array',
    },
  },

  'availability.*.slots.*.startTime': {
    in: ['body'],
    optional: true,
    matches: {
      options: [/^([01]\d|2[0-3]):([0-5]\d):([0-5]\d)$/],
      errorMessage: 'startTime must be a valid time (HH:mm:ss)',
    },
  },

  'availability.*.slots.*.endTime': {
    in: ['body'],
    optional: true,
    matches: {
      options: [/^([01]\d|2[0-3]):([0-5]\d):([0-5]\d)$/],
      errorMessage: 'endTime must be a valid time (HH:mm:ss)',
    },
  },

  'address.zipcode': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'zipcode must be a string',
    },
  },

  'address.city': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'city must be a string',
    },
  },

  'address.state': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'state must be a string',
    },
  },

  'address.addressLine1': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'addressLine1 must be a string',
    },
  },

  'address.latitude': {
    in: ['body'],
    optional: true,
    isFloat: {
      options: { min: -90, max: 90 },
      errorMessage: 'latitude must be a valid number between -90 and 90',
    },
  },

  'address.longitude': {
    in: ['body'],
    optional: true,
    isFloat: {
      options: { min: -180, max: 180 },
      errorMessage: 'longitude must be a valid number between -180 and 180',
    },
  },

  'address.radius': {
    in: ['body'],
    optional: true,
    isDecimal: {
      errorMessage: 'radius must be a decimal number',
    },
  },
};
