/**
 * Test script to verify the chatbot conversation fix
 * This script tests that the AI assistant doesn't repeat its introduction
 * and properly maintains conversation context.
 */

require('dotenv/config');

const {
  handleGeneralConversation,
} = require('./services/GeneralConversationService');
const ChatMemoryService = require('./services/ChatMemoryService');

async function testChatbotConversation() {
  console.log('🧪 Testing Chatbot Conversation Fix...\n');

  const sessionId = 'test-session-' + Date.now();

  try {
    // Test 1: First message should include introduction
    console.log('📝 Test 1: First message (should include introduction)');
    const firstMessage = 'my car is not starting';
    console.log(`User: ${firstMessage}`);

    const firstResponse = await handleGeneralConversation(
      firstMessage,
      sessionId
    );
    console.log(`Revi: ${firstResponse.message}\n`);

    // Check if first response includes introduction
    const hasIntroduction =
      firstResponse.message.toLowerCase().includes("hi there! i'm revi") ||
      firstResponse.message.toLowerCase().includes("i'm revi");
    console.log(
      `✅ First response includes introduction: ${hasIntroduction}\n`
    );

    // Test 2: Second message should NOT include introduction
    console.log(
      '📝 Test 2: Follow-up message (should NOT include introduction)'
    );
    const secondMessage = 'see why its saying again and again';
    console.log(`User: ${secondMessage}`);

    const secondResponse = await handleGeneralConversation(
      secondMessage,
      sessionId
    );
    console.log(`Revi: ${secondResponse.message}\n`);

    // Check if second response does NOT include introduction
    const hasRepeatedIntroduction =
      secondResponse.message.toLowerCase().includes("hi there! i'm revi") ||
      (secondResponse.message.toLowerCase().includes("i'm revi") &&
        secondResponse.message.toLowerCase().includes('automotive assistant'));
    console.log(
      `✅ Second response avoids repeated introduction: ${!hasRepeatedIntroduction}\n`
    );

    // Test 3: Third message should continue conversation naturally
    console.log('📝 Test 3: Third message (should continue naturally)');
    const thirdMessage = 'it should speak it once right?';
    console.log(`User: ${thirdMessage}`);

    const thirdResponse = await handleGeneralConversation(
      thirdMessage,
      sessionId
    );
    console.log(`Revi: ${thirdResponse.message}\n`);

    // Test 4: Check conversation history
    console.log('📝 Test 4: Checking conversation history');
    const history = await ChatMemoryService.getFormattedHistory(sessionId);
    console.log('Conversation History:');
    console.log(history);
    console.log('\n');

    // Verify history contains all messages
    const historyLines = history.split('\n').filter((line) => line.trim());
    const expectedMessages = 6; // 3 user messages + 3 assistant responses
    console.log(
      `✅ History contains expected number of messages: ${historyLines.length >= expectedMessages}\n`
    );

    // Clean up
    ChatMemoryService.clearHistory(sessionId);
    console.log('🧹 Test session cleaned up');

    console.log('\n🎉 Chatbot conversation test completed!');
    console.log('Summary:');
    console.log(`- First response includes introduction: ${hasIntroduction}`);
    console.log(
      `- Follow-up responses avoid repeated introduction: ${!hasRepeatedIntroduction}`
    );
    console.log(
      `- Conversation history properly maintained: ${historyLines.length >= expectedMessages}`
    );
  } catch (error) {
    console.error('❌ Test failed with error:', error);
    // Clean up on error
    ChatMemoryService.clearHistory(sessionId);
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testChatbotConversation()
    .then(() => {
      console.log('\n✅ Test execution completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { testChatbotConversation };
