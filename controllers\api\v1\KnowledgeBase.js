const KnowledgeBaseRepository = require('../../../models/repositories/KnowledgeBaseRepository');
const { genRes, errorMessage, resCode } = require('../../../config/options');

exports.getKnowledgeBaseListing = async (req, res) => {
  try {
    const { start = 0, limit = 10, userType } = req.query;

    const { message, data } =
      await KnowledgeBaseRepository.getActiveKnowledgeBaseAndCount({
        start: parseInt(start),
        limit: parseInt(limit),
        userType,
      });

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};
