const { complianceLegalTitle } = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const ComplianceLegal = sequelize.define(
    'ComplianceLegal',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      title: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: complianceLegalTitle.TERMS_AND_CONDITIONS,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
    },
    {
      tableName: 'ComplianceLegal',
      timestamps: true,
    }
  );

  ComplianceLegal.associate = function (models) {
    ComplianceLegal.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'user',
      onDelete: 'CASCADE',
    });
  };

  return ComplianceLegal;
};
